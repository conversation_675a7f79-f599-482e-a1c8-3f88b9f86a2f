{"name": "listen1", "version": "2.33.0", "description": "One for all free music in China", "main": "app/main.js", "scripts": {"postinstall": "electron-builder install-app-deps", "start": "electron ./app --enable-logging", "dev": "NODE_ENV='development' npm run start", "dist:mac": "CSC_IDENTITY_AUTO_DISCOVERY=false DEBUG=electron-builder electron-builder --mac", "dist": "electron-builder .", "dist:linux": "electron-builder --linux --ia32 --x64", "dist:linux32": "electron-builder --linux --ia32", "dist:linux64": "electron-builder --linux --x64", "dist:linuxArm64": "electron-builder --linux --arm64", "dist:linuxArmv7l": "electron-builder --linux --armv7l", "dist:win": "electron-builder --win"}, "repository": {"type": "git", "url": "git+https://github.com/listen1/listen1_desktop.git"}, "keywords": ["Electron", "Listen 1"], "author": "Listen 1 <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/listen1/listen1_desktop/issues"}, "homepage": "https://github.com/listen1/listen1_desktop#readme", "build": {"appId": "com.listen1.listen1", "productName": "Listen1", "asar": true, "artifactName": "${name}_${version}_${os}_${arch}.${ext}", "dmg": {"icon": "build/disk.icns", "contents": [{"x": 192, "y": 344}, {"x": 448, "y": 344, "type": "link", "path": "/Applications"}]}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64", "universal"]}], "category": "public.app-category.music"}, "linux": {"target": ["tar.gz", "appImage", "deb"], "category": "Audio"}, "nsis": {"runAfterFinish": false, "deleteAppDataOnUninstall": true, "allowToChangeInstallationDirectory": true, "oneClick": false, "installerLanguages": "zh_CN", "language": 2052, "perMachine": true, "createDesktopShortcut": true}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32", "arm64"]}, {"target": "7z", "arch": ["x64", "ia32", "arm64"]}], "icon": "build/icon.ico"}}, "devDependencies": {"electron": "^32.3.2", "electron-builder": "^25.1.8", "prettier": "^2.6.2"}}