!function(t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).i18nextHttpBackend=t()}(function(){return function o(i,r,a){function s(e,t){if(!r[e]){if(!i[e]){var n="function"==typeof require&&require;if(!t&&n)return n(e,!0);if(u)return u(e,!0);throw(n=new Error("Cannot find module '"+e+"'")).code="MODULE_NOT_FOUND",n}n=r[e]={exports:{}},i[e][0].call(n.exports,function(t){return s(i[e][1][t]||t)},n,n.exports,o,i,r,a)}return r[e].exports}for(var u="function"==typeof require&&require,t=0;t<a.length;t++)s(a[t]);return s}({1:[function(n,o,i){!function(e){!function(){var t;"function"==typeof fetch&&(void 0!==e&&e.fetch?t=e.fetch:"undefined"!=typeof window&&window.fetch&&(t=window.fetch)),void 0===n||"undefined"!=typeof window&&void 0!==window.document||((t=t||n("node-fetch")).default&&(t=t.default),i.default=t,o.exports=i.default)}.call(this)}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"node-fetch":5}],2:[function(t,e,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o,i=t("./utils.js"),r=(o=t("./request.js"))&&o.__esModule?o:{default:o};function a(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}function s(){return{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",allowMultiLoading:!1,parse:function(t){return JSON.parse(t)},stringify:JSON.stringify,parsePayload:function(t,e,n){return o=n||"",(n=e)in(e={})?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o,e;var o},request:r.default,reloadInterval:"undefined"==typeof window&&36e5,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}}var u,f,c,l=(u=d,(f=[{key:"init",value:function(t){var e=this,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};this.services=t,this.options=(0,i.defaults)(n,this.options||{},s()),this.allOptions=o,this.services&&this.options.reloadInterval&&setInterval(function(){return e.reload()},this.options.reloadInterval)}},{key:"readMulti",value:function(t,e,n){var o=this.options.loadPath;"function"==typeof this.options.loadPath&&(o=this.options.loadPath(t,e));o=this.services.interpolator.interpolate(o,{lng:t.join("+"),ns:e.join("+")});this.loadUrl(o,n,t,e)}},{key:"read",value:function(t,e,n){var o=this.options.loadPath;"function"==typeof this.options.loadPath&&(o=this.options.loadPath([t],[e]));o=this.services.interpolator.interpolate(o,{lng:t,ns:e});this.loadUrl(o,n,t,e)}},{key:"loadUrl",value:function(i,r,a,s){var u=this;this.options.request(this.options,i,void 0,function(t,e){if(e&&(500<=e.status&&e.status<600||!e.status))return r("failed loading "+i+"; status code: "+e.status,!0);if(e&&400<=e.status&&e.status<500)return r("failed loading "+i+"; status code: "+e.status,!1);if(!e&&t&&t.message&&-1<t.message.indexOf("Failed to fetch"))return r("failed loading "+i+": "+t.message,!0);if(t)return r(t,!1);var n,o;try{n="string"==typeof e.data?u.options.parse(e.data,a,s):e.data}catch(t){o="failed parsing "+i+" to json"}if(o)return r(o,!1);r(null,n)})}},{key:"create",value:function(n,o,t,e,i){var r,a,s,u,f=this;this.options.addPath&&("string"==typeof n&&(n=[n]),r=this.options.parsePayload(o,t,e),a=0,s=[],u=[],n.forEach(function(t){var e=f.options.addPath;"function"==typeof f.options.addPath&&(e=f.options.addPath(t,o));t=f.services.interpolator.interpolate(e,{lng:t,ns:o});f.options.request(f.options,t,r,function(t,e){a+=1,s.push(t),u.push(e),a===n.length&&i&&i(s,u)})}))}},{key:"reload",value:function(){var e,t=this,n=this.services,i=n.backendConnector,o=n.languageUtils,r=n.logger,a=i.language;a&&"cimode"===a.toLowerCase()||(e=[],(n=function(t){o.toResolveHierarchy(t).forEach(function(t){e.indexOf(t)<0&&e.push(t)})})(a),this.allOptions.preload&&this.allOptions.preload.forEach(n),e.forEach(function(o){t.allOptions.ns.forEach(function(n){i.read(o,n,"read",null,null,function(t,e){t&&r.warn("loading namespace ".concat(n," for language ").concat(o," failed"),t),!t&&e&&r.log("loaded namespace ".concat(n," for language ").concat(o),e),i.loaded("".concat(o,"|").concat(n),t,e)})})}))}}])&&a(u.prototype,f),c&&a(u,c),d);function d(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};!function(t){if(!(t instanceof d))throw new TypeError("Cannot call a class as a function")}(this),this.services=t,this.options=e,this.allOptions=n,this.type="backend",this.init(t,e,n)}l.type="backend",n.default=l,e.exports=n.default},{"./request.js":3,"./utils.js":4}],3:[function(n,o,i){!function(e){!function(){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var r,s,u,a=n("./utils.js"),t=function(t){if(t&&t.__esModule)return t;if(null===t||"object"!==c(t)&&"function"!=typeof t)return{default:t};var e=f();if(e&&e.has(t))return e.get(t);var n,o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(n in t){var r;Object.prototype.hasOwnProperty.call(t,n)&&((r=i?Object.getOwnPropertyDescriptor(t,n):null)&&(r.get||r.set)?Object.defineProperty(o,n,r):o[n]=t[n])}o.default=t,e&&e.set(t,o);return o}(n("./getFetch.js"));function f(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return f=function(){return t},t}function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}"function"==typeof fetch&&(void 0!==e&&e.fetch?r=e.fetch:"undefined"!=typeof window&&window.fetch&&(r=window.fetch)),a.hasXMLHttpRequest&&(void 0!==e&&e.XMLHttpRequest?s=e.XMLHttpRequest:"undefined"!=typeof window&&window.XMLHttpRequest&&(s=window.XMLHttpRequest)),"function"==typeof ActiveXObject&&(void 0!==e&&e.ActiveXObject?u=e.ActiveXObject:"undefined"!=typeof window&&window.ActiveXObject&&(u=window.ActiveXObject)),"function"!=typeof(r=!r&&t&&!s&&!u?t.default||t:r)&&(r=void 0);function l(t,e){if(e&&"object"===c(e)){var n,o="";for(n in e)o+="&"+encodeURIComponent(n)+"="+encodeURIComponent(e[n]);if(!o)return t;t=t+(-1!==t.indexOf("?")?"&":"?")+o.slice(1)}return t}t=function(t,e,n,o){return"function"==typeof n&&(o=n,n=void 0),o=o||function(){},r?function(t,e,n,o){t.queryStringParams&&(e=l(e,t.queryStringParams));var i=(0,a.defaults)({},"function"==typeof t.customHeaders?t.customHeaders():t.customHeaders);n&&(i["Content-Type"]="application/json"),r(e,(0,a.defaults)({method:n?"POST":"GET",body:n?t.stringify(n):void 0,headers:i},"function"==typeof t.requestOptions?t.requestOptions(n):t.requestOptions)).then(function(e){return e.ok?void e.text().then(function(t){o(null,{status:e.status,data:t})}).catch(o):o(e.statusText||"Error",{status:e.status})}).catch(o)}(t,e,n,o):a.hasXMLHttpRequest||"function"==typeof ActiveXObject?function(t,e,n,o){n&&"object"===c(n)&&(n=l("",n).slice(1)),t.queryStringParams&&(e=l(e,t.queryStringParams));try{var i=s?new s:new u("MSXML2.XMLHTTP.3.0");i.open(n?"POST":"GET",e,1),t.crossDomain||i.setRequestHeader("X-Requested-With","XMLHttpRequest"),i.withCredentials=!!t.withCredentials,n&&i.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),i.overrideMimeType&&i.overrideMimeType("application/json");var r=t.customHeaders;if(r="function"==typeof r?r():r)for(var a in r)i.setRequestHeader(a,r[a]);i.onreadystatechange=function(){3<i.readyState&&o(400<=i.status?i.statusText:null,{status:i.status,data:i.responseText})},i.send(n)}catch(t){console&&console.log(t)}}(t,e,n,o):void 0};i.default=t,o.exports=i.default}.call(this)}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./getFetch.js":1,"./utils.js":4}],4:[function(t,e,n){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Object.defineProperty(n,"__esModule",{value:!0}),n.defaults=function(n){return i.call(r.call(arguments,1),function(t){if(t)for(var e in t)void 0===n[e]&&(n[e]=t[e])}),n},n.hasXMLHttpRequest=function(){return"function"==typeof XMLHttpRequest||"object"===("undefined"==typeof XMLHttpRequest?"undefined":o(XMLHttpRequest))};var n=[],i=n.forEach,r=n.slice},{}],5:[function(t,e,n){},{}]},{},[2])(2)});