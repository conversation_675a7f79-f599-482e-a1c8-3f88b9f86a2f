html,
body {
  margin: 0;
  padding: 0;
  font-size: var(--text-default-size);
  color: var(--text-default-color);
  font-family: system-ui, 'PingFang SC', STHeiti, sans-serif;
 
}
body .body{
  background-color: var(--color-body-bg);
  transition: background 0.2s;
  border-radius: 8px;
}
a {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}
a:hover {
  text-decoration: underline;
}

.wrap {
  /* https://stackoverflow.com/questions/28897089/z-index-on-borders */
  outline: solid 1px var(--windows-border-color);
  box-sizing: border-box;
}

/* remove focus highlight */
input:focus,
select:focus,
textarea:focus,
button:focus {
  outline: none;
}
input:focus,
textarea:focus {
  background-color: var(--theme-color-hover);
  color: var(--theme-color);
}
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

input,
svg,
.icon {
  -webkit-app-region: no-drag;
}

button {
  background-color: var(--button-background-color);
  color: var(--text-default-color);
  cursor: pointer;
  border: solid 1px var(--button-background-color);
  border-radius: var(--default-border-radius);
  padding: 5px;
  min-width: 80px;
  min-height: 32px;
}
button:hover {
  background-color: var(--button-hover-background-color);
}
img {
  -webkit-user-drag: none;
  object-fit: cover;
}
.l1-button {
  background-color: var(--button-background-color);
  color: var(--text-default-color);
  border-radius: var(--default-border-radius);
  padding: 5px;
  margin-right: 4px;
  color: var(--text-default-color);
  cursor: pointer;
  display: inline-block;
}
.l1-button:hover {
  background: var(--button-hover-background-color);
  color: var(--text-default-color);
}
svg {
  width: 24px;
  height: 24px;
  stroke: currentColor;
  stroke-width: 1;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
  cursor: pointer;
  /*    stroke: var(--icon-default-color);*/
}

.icon {
  /* default icon settings */
  font-size: 16px;
  cursor: pointer;
}

/* tools utils */
.flex-scroll-wrapper {
  flex: 1;
  height: 100px;
  /* overflow-y: scroll; */
  scrollbar-width: thin;
  scrollbar-color: var(--scroll-color) var(--content-background-color);
}

/* scroll bar style */
::-webkit-scrollbar {
  width: 8px;
  background: transparent;
  transition: background 0.4s;
}

::-webkit-scrollbar-thumb {
  transition: background 0.4s;
  width: 8px;
  border-radius: 4px;
  background: hsla(0, 0%, 50.2%, 0.38);
  /*rgba(151, 151, 151, 0.4);*/

  /*    -webkit-box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05), inset 1px 1px 0px rgba(0, 0, 0, 0.05);*/
}

::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-thumb:active {
  background-color: hsla(0, 0%, 50.2%, 0.58);
  transition: background 0.4s;
}

::-webkit-scrollbar-button {
  width: 0;
  height: 0;
  display: none;
}

::-webkit-scrollbar-corner {
  background-color: transparent;
}

/* main framework start */
.wrap {
  display: flex;
  height: 100vh;
  flex-direction: column;
  margin: auto;
}

/* split screen to up/down 2 parts */
.main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* split main to left/right 2 parts */
.main .sidebar {
  padding-left: 1vw;
  display: flex;
  flex-direction: column;
}

.main .content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* split content to up/down 2 parts */
.main .content .navigation {
  user-select: none;
  height: 64px;
  flex: 0 0 64px;
  display: flex;
  align-items: center;
  -webkit-app-region: drag;
  margin-right: 20px;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  backdrop-filter: saturate(180%) blur(20px);
  background-color: var(--nav-background-color);
  transition: background 0.2s;
}

.main .content .browser {
  flex: 1;
}

/* main framework end */

/*****************************************************************/

/* main sidebar start */
.main .sidebar-content {
  height: calc(100vh - 210px);
  overflow: hidden;
  width: 60px;
  transition: 0.2s;
  background: var(--sidebar-background);
  border-radius: 10px;
  cursor: default;
}
.main .sidebar-content.footerdef {
  height: calc(100vh - 130px);
}
.main .sidebar-content.opensidebar {
  width: 200px;
}
.sidebar-content .logo-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 10px;
  margin-bottom: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--sidebar-splitter);
  transition: 0.2s;
  cursor: pointer;
}
.sidebar-content .logo-content .logo-svg {
  padding: 10px;
  padding-right: 0;
  transition: 0.2s;
}
.sidebar-content .logo-content .logo-title {
  padding-right: 10px;
  display: flex;
}
.sidebar-content .logo-content .logo-title svg {
  color: var(--sidebar-splitter);
  opacity: 0;
  width: 0;
  transition: 0.2s;
}
.opensidebar > .logo-content {
  border-bottom: 1px solid transparent;
}
.opensidebar > .logo-content .logo-title svg {
  opacity: 1;
  width: 90px;
}
.sidebar .sidebar-scroll-content {
  overflow-x: hidden;
  overflow-y: overlay;

  height: calc(100% - 80px);
}
.sidebar .sidebar-scroll-content::-webkit-scrollbar {
  display: none;
}
.sidebar .sidebar-scroll-content::-webkit-scrollbar-thumb {
  background: var(--theme-color-ope);
}
.sidebar .opensidebar > .sidebar-scroll-content:hover::-webkit-scrollbar {
  display: block;
  width: 2px;
}
.sidebar .menu-control {
  height: 74px;
  -webkit-app-region: drag;
}

.sidebar .menu-title {
  height: 28px;
  line-height: 28px;
  margin: 8px 12px 8px 12px;
  color: var(--link-default-color);
  padding-left: 10px;
  display: flex;
  align-items: center;
  font-size: 12px;
}
.sidebar .menu-title .title {
  user-select: none;
  white-space: nowrap;
  opacity: 0;
  transition: 0.2s;
  width: 0;
  flex: 0;
}
.sidebar .menu-title .title.opensidebar {
  opacity: 1;
  flex: 1;
  width: auto;
}
.sidebar ul li .sidebar-block > div {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
.sidebar ul li .sidebar-block .sidebar .menu-title svg {
  flex: 0 0 18px;
}

.sidebar ul li {
  cursor: pointer;
  padding: 2px 10px;
}
.sidebar ul li .sidebar-block {
  display: flex;
  align-items: center;
  line-height: 28px;
  padding: 6px 12px;
  margin: 1px 0;
  transition: all 0.2s;

  color: var(--sidebar-hover-text-color);
  border-radius: var(--default-border-radius);
  background-color: var(--sidebar-button-background);
}
.sidebar ul li .sidebar-block.opensidebar {
  background-color: transparent;
}
.sidebar svg {
  width: 18px;
  height: 18px;
  z-index: 10;
}
.sidebar .icon {
  margin-right: 10px;
  font-size: 18px;
}
.sidebar ul li a {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.sidebar ul li:hover .sidebar-block {
  background: var(--theme-color-hover);
  color: var(--text-default-color);
  border-radius: 10px;
  transition: all 0.3s;
}
.sidebar ul li:hover .sidebar-block.opensidebar {
  background: var(--sidebar-button-background);
}

.sidebar ul li.active .sidebar-block,
.sidebar ul li.active:hover .sidebar-block {
  background: var(--theme-color);
  color: #fff;
  border-radius: 10px;
}
.sidebar ul li.dragover .sidebar-block {
  background: var(--sidebar-highlight-background-color);
  color: var(--text-default-color);
  border-radius: 10px;
}
/*
avoid hover effect trigger dragleave event
https://stackoverflow.com/questions/19889615/can-an-angular-directive-pass-arguments-to-functions-in-expressions-specified-in
*/
.sidebar ul li * {
  pointer-events: none;
}
/* main sidebar end */

/* widget navigation start */
.navigation svg {
  width: 24px;
  height: 24px;
}
.navigation .icon svg {
  color: var(--text-default-color);
}
.navigation .backfront {
  flex: 0 0 45px;
  line-height: 46px;
  vertical-align: middle;
  padding: 0 13px;
  flex: 1;
}

.navigation .search {
  display: flex;
  width: 200px;
  height: 32px;
  background: var(--search-input-background-color);
  border-style: none;
  border-radius: var(--default-border-radius);
  padding-left: 10px;
  margin-right: 20px;
  align-items: center;
}
.navigation .settings {
  margin-right: 8px;
}

.navigation .settings.is-setting {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
  margin-right: 1vw;
  transition: 0.2s;
}
.navigation .settings.is-setting:hover {
  opacity: 1;
  background-color: var(--songlist-hover-background-color);

  border-radius: 25%;
}
.navigation .settings.is-setting:hover svg {
  color: var(--text-default-color);
}

.navigation .icon {
  color: var(--text-default-color);
  background-color: transparent;
  /* opacity: 0.5; */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 25%;
  transition: 0.2s;
}
.navigation .icon img {
  border-radius: 50%;
  width: 30px;
  height: 30px;
}

.navigation .icon:hover {
  opacity: 1;
  background-color: var(--songlist-hover-background-color);
}

.navigation .backfront .icon {
  display: inline-block;
  vertical-align: middle;
}

.navigation .backfront .icon:nth-of-type(1) {
  margin-right: 8px;
}

.navigation .search-input {
  width: 174px;
  background-color: transparent;
  border-style: none;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-default-color);
}

.navigation .window-control {
  display: flex;
}

.navigation .window-control svg {
  margin-left: 8px;
}

.navigation .window-control svg:first-of-type {
  margin-left: 15px;
}

/* navigation end */

/* page hot-playlist start */

.playlist-covers,
.detail-songlist.isSearch {
  padding-right: 2vw;
  padding-top: 30px;
  display: flex;
  flex-flow: row wrap;
  position: relative;

  margin: 0 14px;
  grid-template-columns: repeat(5, 1fr);
  gap: 40px 0px;
}
.playlist-covers {
  transition: padding 0.3s;
}
.playlist-covers li {
  color: var(--text-default-color);
  margin: 0 12px;
}
@media screen and (max-width:1000px){
  .playlist-covers li {
    flex: 0 1 calc(25% - 26px);
  }
}

@media screen and (min-width:1000px) and (max-width:1480px){
  .playlist-covers li {
    flex: 0 1 calc(20% - 26px);
  }
}

@media screen and (min-width:1480px){
  .playlist-covers li {
    flex: 0 1 calc(16.66% - 26px);
  }
}
.playlist-covers .u-cover,
ul.detail-songlist li .u-cover {
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  user-select: none;
}

.playlist-covers .u-cover img,
ul.detail-songlist li .u-cover img {
  transition: all 0.1s ease-in-out 0s;
  box-sizing: border-box;
  width: 100%;
  border-radius: 0.75em;
  /* min-width: 136px;
  max-width: 100%; */
  object-fit: cover;
  border: solid 1px rgba(0, 0, 0, 0.04);
  cursor: pointer;
  z-index: 1;
}

.playlist-covers .u-cover .bottom,
.detail-head-cover .bottom,
ul.detail-songlist li .u-cover .bottom {
  position: absolute;
  z-index: 2;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease 0s;

  display: flex;
  align-items: center;
  justify-content: center;
  color: rgb(255, 255, 255);
  backdrop-filter: blur(8px);
  background: hsla(0, 0%, 100%, 0.14);
  border: 1px solid hsla(0, 0%, 100%, 0.08);
  height: 22%;
  width: 22%;
  border-radius: 50%;
}
.playlist-covers .u-cover .covershadow,
.detail-head-cover .covershadow,
ul.detail-songlist li .u-cover .covershadow {
  transition: all 0.4s;
  opacity: 0;
  position: absolute;
  top: 12px;
  height: 100%;
  width: 100%;
  filter: blur(16px) opacity(0.6);
  transform: scale(0.92, 0.96);
  z-index: 0;
  background-size: cover;
  border-radius: 0.75em;
}
.playlist-covers .u-cover:hover img,
ul.detail-songlist li .u-cover:hover img {
  margin-top: -10px;
  margin-bottom: 10px;
  padding-bottom: 0;
}
.playlist-covers .u-cover:hover .covershadow,
ul.detail-songlist li .u-cover:hover .covershadow {
  display: block;
  opacity: 1;
}
.playlist-covers .u-cover:hover .bottom,
.detail-head-cover:hover .bottom,
ul.detail-songlist li .u-cover:hover .bottom {
  opacity: 1;
}
.playlist-covers .u-cover:hover .bottom:hover,
.detail-head-cover:hover .bottom:hover,
ul.detail-songlist li .u-cover .bottom:hover {
  background: hsla(0, 0%, 100%, 0.28);
}

.playlist-covers .u-cover .bottom svg,
ul.detail-songlist li .u-cover .bottom svg {
  height: 30px;
  width: 30px;
  fill: rgba(200, 200, 200, 0.5);
  stroke-width: 1;
  stroke: #ffffff;
}

.playlist-covers .u-cover .bottom svg:hover,
ul.detail-songlist li .u-cover .bottom svg:hover {
  fill: rgba(100, 100, 100, 0.5);
}

.playlist-covers .desc,
ul.detail-songlist li .desc {
  cursor: default;
  padding-top: 8px;
  height: 65px;
}

.playlist-covers .desc .title,
ul.detail-songlist li .desc div.title {
  word-break: break-all;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  word-break: break-all;
  display: flex;
  margin: 0 0 5px;
  z-index: 1;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  text-decoration: none;
  overflow: hidden;
}

/* .playlist-covers .desc .title:hover,
ul.detail-songlist li .desc div.title:hover{
  text-decoration: underline;
} */
/* page hot-playlist end */

/* page playlist-detail start */
.page .playlist-detail {
  padding-bottom: 37px;
}

.page .playlist-detail .detail-head {
  display: flex;
  margin-top: 11px;
  margin-bottom: 72px;
}

.page .playlist-detail .detail-head img {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
  border-radius: 0.75em;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  aspect-ratio: 1/1;
}

.page .playlist-detail .detail-head .covershadow {
  transition: all 0.4s;
  opacity: 0;
  position: absolute;
  top: 12px;
  height: 100%;
  width: 100%;
  filter: blur(16px) opacity(0.6);
  transform: scale(0.92, 0.96);
  z-index: 0;
  background-size: cover;
  border-radius: 0.75em;
}

.page .playlist-detail .detail-head .detail-head-cover {
  position: relative;
  margin-left: 2vw;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  height: 25vh;
  width: 25vh;
}

.page .playlist-detail .detail-head .detail-head-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 56px;
  margin-right: 2vw;
}
.page .playlist-detail .detail-head .detail-head-title h2 {
  font-size: 36px;
  font-weight: 700;
  margin-top: 10px;
}

.playlist-button-list {
  display: flex;
  flex-flow: row wrap;
}

.playlist-button-list .playlist-button {
  margin-top: 10px;
  height: 26px;
  cursor: pointer;
  display: flex;
  margin-right: 16px;
  border-radius: 8px;
  padding: 8px 16px;
  width: auto;
  background-color: var(--button-background-color);
}
.playlist-button-list .playlist-button.favorited {
  background-color: var(--theme-color-hover);
  color: var(--theme-color);
}

.playlist-button-list .playlist-button.playadd-button {
  flex: 0 0 136px;
}

.playlist-button-list .playlist-button .play-list {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 17px;
  line-height: 17px;
  font-weight: 500;
  user-select: none;
}
.playlist-button-list .playlist-button .play-list svg {
  margin-right: 4px;
}

.playlist-button-list .playlist-button.playadd-button .play-list svg {
  width: 14px;
  height: 14px;
  flex: 0 0 14px;
  margin-right: 4px;
  stroke: var(--important-color);
  fill: var(--important-color);
}
.playlist-button-list .playlist-button .play-list .icon {
  margin-right: 8px;
}
.playlist-button-list .playlist-button.playadd-button .play-list .icon {
  flex: 0 0 14px;
  margin-right: 4px;
  color: var(--important-color);
  transition: 0.2s;
}

.playlist-button-list .playlist-button.playadd-button .add-list {
  flex: 0 0 26px;
  height: 26px;
  width: 26px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.2s;
  font-size: 17px;
  line-height: 17px;
  font-weight: 500;
}

.playlist-button-list .playlist-button.edit-button .play-list.favorited {
  color: var(--text-default-color);
}
.playlist-button-list .playlist-button.edit-button .play-list.notfavorite {
  color: var(--text-default-color);
}

.playlist-button-list .playlist-button:hover,
.playlist-button-list .playlist-button.playadd-button .add-list:hover,
.playlist-button-list .playlist-button.playadd-button .play-list:hover {
  transform: scale(1.1);
  transition: 0.2s;
}
.playlist-button-list .playlist-button.playadd-button .add-list svg {
  width: 14px;
  height: 14px;
}

.playlist-button-list .playlist-button.clone-button,
.playlist-button-list .playlist-button.edit-button,
.playlist-button-list .playlist-button.fav-button {
  flex: 0 0 auto;
}

.playlist-button-list .playlist-button.clone-button .play-list svg,
.playlist-button-list .playlist-button.edit-button .play-list svg,
.playlist-button-list .playlist-button.fav-button .play-list svg {
  width: 16px;
  height: 16px;
  flex: 0 0 16px;
  margin-right: 8px;
  stroke: rgb(102, 102, 102);
}

.playlist-button-list .playlist-button.fav-button .play-list.favorited svg {
  fill: rgb(102, 102, 102);
}

/* page playlist detail end */

/* page song detail start */
.songdetail-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 100px;
  overflow: hidden;
  -webkit-app-region: no-drag;
  transition: all 0.5s;
  z-index: 100;
  opacity: 1;
}

.songdetail-wrapper .draggable-zone {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  -webkit-app-region: drag;
  height: 80px;
}

.songdetail-wrapper.slidedown .draggable-zone {
  display: none;
  -webkit-app-region: no-drag;
}

.songdetail-wrapper.slidedown {
  top: calc(100% - 100px);
  /* opacity: 0; */
}

.songdetail-wrapper .close {
  position: absolute;
  top: 24px;
  left: 24px;
  height: 19px;
  width: 19px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  -webkit-app-region: no-drag;
  transition: 0.2s;
  z-index: 100;
}
.songdetail-wrapper .close:hover {
  background-color: var(--songlist-hover-background-color);
}
.songdetail-wrapper .close.mac {
  top: 44px;
}

.songdetail-wrapper .window-control {
  position: absolute;
  top: 24px;
  right: 24px;
  height: 24px;
  cursor: pointer;
  -webkit-app-region: no-drag;
  z-index: 99;
}

.songdetail-wrapper .window-control svg {
  margin-left: 8px;
  stroke: var(--now-playing-close-icon-color);
}

.songdetail-wrapper .close svg {
  stroke: var(--now-playing-close-icon-color);
}
.bgwrapper {
  overflow: hidden;
  border-radius: 10px;
  width: 100%;
}
.bg {
  opacity: 0.6;
  width: 100%;
  height: 100%;
  filter: blur(200px) contrast(75%) brightness(150%);
  float: left;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  transition: background ease-in-out 1.5s;
}
.playsong-detail {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  display: flex;
  clip: rect(auto, auto, auto, auto);
}

.playsong-detail .detail-head {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  margin-right: 32px;
  margin-top: 24px;
  align-items: center;
  transition: all 0.5s;
  z-index: 1;
}

.playsong-detail .detail-head-cover {
  position: relative;
}

.playsong-detail .detail-head img {
  border-radius: 10px;
  width: 54vh;
  height: 54vh;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -o-object-fit: cover;
  object-fit: cover;
}

.playsong-detail .detail-songinfo {
  flex: 1;
  font-weight: 600;
  color: var(--color-text);
  margin-right: 24px;
  z-index: 0;
}
.playsong-detail .detail-head-title {
  max-width: 54vh;
  margin-top: 24px;
}

.playsong-detail .title {
  display: flex;
  align-items: center;
}
.playsong-detail .title h2 {
  font-size: var(--h2-title-font-size);
  margin-top: 8px;
  margin-bottom: 0;
  font-weight: 600;
  opacity: 0.88;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
.playsong-detail .title .badge {
  font-size: var(--badge-font-size);
  color: var(--theme-color);
  border: solid 1px var(--theme-color);
  border-radius: 5px;
  margin-left: 10px;
  padding-left: 4px;
  padding-right: 4px;
  margin-top: 4px;
  box-sizing: border-box;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}
.playsong-detail .title .badge.platform {
  padding-top: 1px;
}
.playsong-detail .title .badge:first-of-type {
  margin-left: 15px;
}
.playsong-detail .info {
  margin-top: 4px;
  font-size: 16px;
  opacity: 0.58;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.playsong-detail .info .singer {
  display: inline;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.playsong-detail .info .album {
  display: inline;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.coverbg .playsong-detail .info span {
  color: var(--lyric-on-cover-color);
}
.playsong-detail .detail-songinfo .lyric {
  font-size: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-left: 78px;
  max-width: 460px;
  overflow-y: auto;
  transition: 0.5s;

  color: var(--lyric-default-color);
}
.playsong-detail .detail-songinfo .lyric::-webkit-scrollbar {
  display: none;
}

.coverbg .playsong-detail .detail-songinfo .lyric {
  color: var(--lyric-on-cover-color);
}
.playsong-detail .detail-songinfo .lyric .placeholder {
  margin-top: 50vh;
}
.playsong-detail .detail-songinfo .lyric p {
  padding: 18px;
  transition: all 0.2s;
  border-radius: 12px;
  margin: 0;
  opacity: 0.28;
  cursor: default;
  background: transparent;
  color: var(--text-default-color);
}
.playsong-detail .detail-songinfo .lyric p:hover {
  background: hsla(0, 0%, 100%, 0.08);
  opacity: 0.6;
  color: var(--text-default-color);
}
.playsong-detail .detail-songinfo .lyric p.translate {
  margin: 5px 0 0 0;
}
.playsong-detail .detail-songinfo .lyric p.hide {
  display: none;
}
.playsong-detail .detail-songinfo .lyric p.highlight {
  color: var(--text-default-color);
  opacity: 1;
  font-size: 26px;
}
.coverbg .playsong-detail .detail-songinfo .lyric p.highlight {
  color: var(--lyric-important-on-cover-color);
  opacity: 1;
}

ul.detail-songlist {
  position: relative;
}
.detail-songlist.playlist-songlist {
  margin: 0 2vw;
  padding-top: 13px;
  transition: 0.3s;
}
ul.detail-songlist.isSearchOne {
  padding: 0 25px;
}

ul.detail-songlist .playlist-search {
  position: absolute;
  right: 0;
  top: -50px;

  display: flex;
  width: 200px;
  height: 32px;
  background: var(--songlist-odd-background-color);
  border-style: none;
  border-radius: var(--default-border-radius);
  padding-left: 10px;
  margin-right: 40px;
  align-items: center;
}
ul.detail-songlist .playlist-search .playlist-search-icon {
  width: 14px;
  position: absolute;
  left: 7px;
  top: 1px;
}
ul.detail-songlist .playlist-search .playlist-clear-icon {
  width: 14px;
  position: absolute;
  left: 158px;
}
ul.detail-songlist .playlist-search .playlist-search-input {
  width: 174px;
  background-color: transparent;
  border-style: none;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-default-color);
}
ul.detail-songlist .playlist-search:hover,
ul.detail-songlist .playlist-search:active {
  background-color: var(--search-input-background-color);
}

ul.detail-songlist li.isSearchType {
  /* https://stackoverflow.com/questions/4157005/css-positioning-z-index-negative-margins */
  position: relative;

  transition: all 0.3s;
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 12px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
ul.detail-songlist li.isSearchGeDan {
  flex: 0 1 calc(20% - 26px);
  min-height: 156px;
  color: var(--text-default-color);
  margin: 0 12px;
  cursor: default;
}

ul.detail-songlist li.playlist-result.isSearchType {
  height: 80px;
  padding: 0 10px;
}

ul.detail-songlist li > img {
  object-fit: cover;
  border-radius: 8px;
  height: 46px;
  width: 46px;
  margin-right: 20px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  cursor: pointer;
}

ul.detail-songlist li.isSearchType:hover {
  background-color: var(--songlist-hover-background-color);
}

ul.detail-songlist li.isSearchType.playing,
ul.detail-songlist li.isSearchType.playing:hover {
  background-color: var(--theme-color-hover);
  color: var(--theme-color);
}
ul.detail-songlist li .title-and-artist {
  flex: 1;
  display: flex;
}
ul.detail-songlist li .container {
  display: flex;
  flex-direction: column;
}

ul.detail-songlist li a {
  cursor: pointer;
}
ul.detail-songlist li a.disabled {
  color: var(--disable-song-title-color);
}
ul.detail-songlist li a span.source,
ul.detail-songlist span {
  border: solid 1px #ccc;
  border-radius: 4px;
  margin-right: 10px;
  display: inline-block;
  padding: 0 4px;
  color: #ccc;
  font-size: 12px;
  width: 24px;
  text-align: center;
  white-space: nowrap;
  height: min-content;
}
ul.detail-songlist li a span.source.playlist {
  margin-left: 10px;
  margin-right: 0;
}

ul.detail-songlist li .title {
  font-size: 18px;
  font-weight: 600;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;

  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
ul.detail-songlist li.playlist-result .title {
  max-height: 80px;
}

ul.detail-songlist li.playlist-result .title img {
  height: 60px;
  width: 60px;
  display: block;
  margin-right: 10px;
}

ul.detail-songlist li .artist {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* line-height: 17px;
  max-height: 38px; */
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;

  font-size: 13px;
  margin-top: 2px;
  font-size: 13px;
  opacity: 0.68;
}

ul.detail-songlist li .album {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* line-height: 17px;
  max-height: 38px; */
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;

  display: flex;
  font-size: 16px;
  opacity: 0.88;
}

ul.detail-songlist li .tools {
  flex: 0 0 110px;
  display: flex;
  align-items: center;
}
ul.detail-songlist li .tools a:hover {
  color: var(--text-default-color) !important;
}

ul.detail-songlist li .tools .icon {
  height: 16px;
  width: 16px;
  color: #9d9d9d;
  margin-top: 2px;
  margin-right: 10px;
}
/* page song detail end */

/* page login start */
.page .login {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 192px);
}
.page .login .login-logo {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}
.page .login .login-logo img {
  height: 64px;
  margin: 20px;
}
.page .login .login-title {
  font-size: 18px;
  margin-bottom: 10px;
}
.page .login .login-form .login-form_field {
  display: flex;
  align-items: center;
  height: 40px;
  margin: 24px;
  width: 270px;
  border: solid 1px var(--button-background-color);
}
.page .login .login-form .login-form_field input {
  background: var(--content-background-color);
  color: var(--text-default-color);
}
.page .login .login-form .login-form_field input.login-form_field_countrycode {
  flex: 0 0 40px;
  width: 40px;
}
.page .login .login-form .login-form_field svg {
  margin-left: 12px;
  margin-right: 12px;
  color: var(--icon-default-color);
  width: 18px;
  height: 18px;
}
.page .login .login-form .login-form_field input {
  border: none;
  flex: 1;
  font-size: 16px;
}
.page .login .login-form .login-form_field input:focus {
  background-color: var(--theme-color-hover);
  color: var(--theme-color);
}
.page .login .login-submit_button {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-top: 24px;
  padding: 8px;
  width: 270px;
  cursor: pointer;
  border: solid 1px var(--button-border-color);
}
.page .login .login-switcher {
  margin-top: 24px;
  cursor: pointer;
}
.page .login .login-notice {
  width: 270px;
  border-top: 1px solid var(--button-border-color);
  margin-top: 30px;
  padding-top: 12px;
  font-size: 12px;
  color: var(--text-subtitle-color);
}
.page .login .usercard {
  padding: 20px;
  display: flex;
  align-items: center;
  width: 400px;
  background-color: var(--button-background-color);
  margin-bottom: 20px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s;
}
.page .login .usercard .logoin-icon {
  width: 60px;
  height: 60px;
  margin: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.page .login .usercard .logoin-icon svg {
  width: 35px;
  height: 35px;
}
.page .login .usercard:hover,
.page .login .usercard:active {
  background-color: var(--theme-color-hover);
  color: var(--theme-color);
  width: 500px;
}
.page .login .usercard:hover .usercard-info,
.page .login .usercard:active .usercard-info {
  color: var(--theme-color);
}
.page .login .usercard img {
  border-radius: 10px;
  width: 60px;
  height: 60px;
  margin: 10px;
}
.page .login .usercard .usercard-title {
  margin-left: 10px;
  flex: 1;
  height: 50px;
  font-size: 18px;
  font-weight: 700;
}
.page .login .usercard .usercard-title .usercard-info {
  color: var(--text-subtitle-color);
  font-size: 12px;
}
.page .login .usercard button {
  margin: 10px;
  margin: 10px;
  font-size: 14px;
  font-weight: 600;
  padding: 8px 16px;
  transition: 0.2s;
}

.page .login .usercard button:hover {
  background-color: var(--theme-color-hover);
  color: var(--theme-color);
}
/* page login end */

/* page setting start */

.page .settings-title {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  font-weight: bold;
  padding-bottom: 12px;
  font-size: 26px;
  user-select: none;
  border-bottom: 1px solid rgba(128, 128, 128, 0.18);
}

.page .settings-content {
  max-width: 800px;
  margin: 25px auto;
  padding: 0 20px;
  font-size: 16px;
  font-weight: 500;
  opacity: 0.78;
}
.page .settings-content label.upload-button,
.page .settings-content .language-button,
.page .settings-content .theme-button,
.page .settings-content button {
  background: var(--button-background-color);
  margin-right: 4px;
  color: var(--text-default-color);
  cursor: pointer;
  opacity: 1;
  font-weight: 600;
  border: none;
  padding: 8px 12px;
  border-radius: 8px;
  appearance: none;
  transition: all 0.2s;
}

.page .settings-content label.upload-button:hover,
.page .settings-content .language-button:hover,
.page .settings-content button:hover {
  transform: scale(1.1);
  background: var(--button-hover-background-color);
}
.page .settings-content .shortcut {
  display: flex;
  margin-bottom: 25px;
}
.page .settings-content .shortcut svg {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}
.page .searchbox .search-pagination {
  text-align: center;
  padding: 32px;
}
.page .searchbox .search-pagination .btn-pagination {
  padding: 8px 16px;
  width: auto;
  height: 40px;
  min-width: 40px;
  font-size: 18px;
  line-height: 18px;
  font-weight: 600;
  transition: 0.2s all;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  opacity: 0.78;
}
.page .searchbox .search-pagination .ng-binding {
  font-size: 18px;
  line-height: 18px;
  font-weight: 600;
  padding: 0 20px;
}
.page .settings-content .shortcut_table .shortcut_table-header,
.page .settings-content .shortcut_table .shortcut_table-line {
  display: flex;
  color: var(--text-default-color);
  box-sizing: border-box;
  align-items: center;
  height: 40px;
}
.page .settings-content .shortcut_table .shortcut_table-header {
  color: var(--link-default-color);
  height: 30px;
}
.page .settings-content .shortcut_table .shortcut_table-function {
  flex: 0 140px;
  padding: 0 10px;
  box-sizing: border-box;
}
.page .settings-content .shortcut_table .shortcut_table-key {
  flex: 0 200px;
  margin-right: 20px;
  box-sizing: border-box;
}
.page .settings-content .shortcut_table .shortcut_table-globalkey {
  flex: 0 240px;
  box-sizing: border-box;
}
.page
  .settings-content
  .shortcut_table
  .shortcut_table-line
  .shortcut_table-key {
  border: solid 1px var(--button-border-color);
  border-radius: 5px;
  padding: 0 10px;
  height: 30px;
  display: flex;
  align-items: center;
}
.page
  .settings-content
  .shortcut_table
  .shortcut_table-line
  .shortcut_table-globalkey {
  border: solid 1px var(--button-border-color);
  border-radius: 5px;
  height: 30px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.page .settings-content .custom-proxy {
  margin-top: 10px;
}
.page .settings-content .custom-proxy .rule-input {
  margin-top: 8px;
}
.page .settings-content .custom-proxy input {
  margin-right: 15px;
  height: 24px;
  width: 200px;
}
.page .settings-content .custom-proxy input:focus {
  background-color: var(--theme-color-hover);
  color: var(--theme-color);
}
.page .settings-content .search-description,
.page .settings-content p {
  margin: 0 0 25px 0;
}
.page .settings-content .search-source-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  line-height: 30px;
}
.page .settings-content .search-source-list .search-source {
  display: flex;
  align-items: center;
  width: 130px;
}
.page .settings-content .search-source-list .search-source svg {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}
/* page setting end */

.loading_bottom {
  display: block;
  width: 40px;
  margin: 0 auto;
}

svg.searchspinner {
  width: 20px;
  height: 20px;
  vertical-align: top;
  margin-left: 15px;
}
/* footer start */

.footer {
  height: 100px;
  display: flex;
  align-items: flex-end;
  z-index: 130;

  margin: 1vh 1vw;
  border-radius: 10px;
  position: fixed;
  bottom: 0;
  width: 98vw;
  transition: 0.5s;
  color: var(--text-default-color);
}

.footer.footerdef {
  opacity: 0;
  bottom: -140px;
  transition: 0.5s;
}
.footer .footer-main {
  position: relative;
  z-index: 140;
  height: 100px;
  border-radius: 10px;
  display: flex;
  flex: 1;
  transition: 0.5s;
  backdrop-filter: saturate(180%) blur(20px);
  background-color: var(--nav-background-color);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0px 0px 16px rgb(0 0 0 / 10%);
  border-top: solid 1px var(--line-default-color);
}
.footer .footer-main.slidedown {
  height: calc(98vh - 2px);
}
.footer .footerwrap {
  width: 100%;
  display: flex;
  height: 100px;
  position: absolute;
  bottom: 0;
}
.footer .left-control {
  flex: 0 0 36%;
  display: flex;
  align-items: center;
  overflow: hidden;
  transition: 0.5s;
  opacity: 1;
}
.footer .left-control.slidedown {
  flex: 0 0 0;
  opacity: 0;
  transform: scaleX(0);
}
.footer .left-control .icon {
  display: flex;
  font-size: 22px;
  border-radius: 10px;
  padding: 7px;
  margin: 37px;
  transition: all 0.3s;
  background: transparent;
}
.footer .left-control .icon:hover {
  background-color: var(--songlist-hover-background-color);
}
.footer .left-control .icon.playlistactive {
  background-color: var(--theme-color-hover);
  color: var(--theme-color);
}
.footer .left-control .splitter {
  height: 20px;
  width: 1px;
  display: inline-block;
  background: #a9a9a9;
}

.footer .left-control .icon.play {
  margin-right: 10px;
}

.footer .left-control .icon.play {
  color: var(--player-icon-color);
}
.footer .left-control .icon.play:hover {
  color: var(--player-icon-hover-color);
}

.footer .main-info {
  flex: 1;
  display: flex;
  z-index: 1;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  z-index: 110;
}

.footer .main-info .logo-banner {
  text-align: center;
  flex: 1;
  display: flex;
  align-items: center;
}

.footer .main-info .logo-banner svg.logo {
  height: 48px;
  width: 48px;
  fill: #666666;
  stroke: #666666;
  margin: 0 auto;
}
.footer .circlemark {
  display: flex;
  justify-content: center;
  width: 100px;
  height: 50px;
  position: absolute;
  top: 45px;
  z-index: -1;
  overflow: hidden;
  transform-origin: top center;
}
.rotatemark {
  animation: rotatemark 1s 1 forwards ease-in-out;
}
.circlmark {
  animation: circlmark 1s 1 forwards ease-in-out;
}
.rotatecircl {
  animation: rotatecircl 16s 0.5s infinite forwards linear;
}
.lipause {
  animation-play-state: paused;
}
.liplay {
  animation-play-state: running;
}
/* .circlopacity{
  animation:circlopacity 1s forwards;
} */
@keyframes rotatemark {
  0% {
    transform-origin: top center;
    transform: rotate(0deg);
  }
  50% {
    transform-origin: top center;
    transform: rotate(180deg);
  }
  100% {
    transform-origin: top center;
    transform: rotate(360deg);
  }
}
@keyframes circlmark {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* @keyframes circlopacity{
  0%{
    opacity: 1;
  }
  50%{
    opacity: 0;
  }
  100%{
    opacity: 1;
  }
} */
@keyframes rotatecircl {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.footer .cover {
  height: 90px;
  width: 90px;
  object-fit: cover;
  position: relative;
  color: transparent;
  top: -30px;
  display: flex;
  justify-content: center;
}
.footer .cover ul,
.footer .cover .cover-list {
  width: 220px;
  height: 90px;
  position: absolute;
}
.footer .cover .cover-list span {
  bottom: 0;
  cursor: pointer;
  transition: 0.3s;
  color: var(--white--black);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
}
.footer .cover .cover-list span:hover,
.footer .cover .cover-list span.show {
  opacity: 1;
  background-color: var(--white--black-background);
}
.footer .cover li {
  transition: all 0.3s;
  left: 0;
  bottom: 0;
  height: 45px;
  width: 30px;
}
.footer .cover li.hid {
  opacity: 0;
  z-index: -1;
  height: 45px;
  width: 32px;
  display: none;
  float: left;
  position: absolute;
  overflow: hidden;
  transition: all 0.3s;
  border-radius: 16px;
  left: 0;
  bottom: 0;
}
.footer .cover li.def {
  display: block;
}
.footer .cover ul .a,
.footer .cover-list .a {
  height: 45px;
  width: 32px;
  left: 0;
  position: absolute;
  overflow: hidden;
  border-radius: 16px;
  opacity: 1;
  z-index: 100;
  display: block;
}
.footer .cover ul .b,
.footer .cover-list .b {
  height: 90px;
  width: 90px;
  left: 65px;
  position: absolute;
  overflow: hidden;
  border-radius: 50%;
  opacity: 1;
  z-index: 101;
  display: block;
}
.footer .cover ul .c,
.footer .cover-list .c {
  height: 45px;
  width: 32px;
  left: 190px;
  position: absolute;
  overflow: hidden;
  border-radius: 16px;
  opacity: 1;
  z-index: 99;
  display: block;
}
.footer .cover img {
  height: 100%;
  width: 100%;
  object-fit: cover;
  /* border-radius: 50%; */
  /* position: absolute; */
  /* border: 1px solid transparent; */
  box-sizing: border-box;
}
.footer .cover .circle {
  width: 100px;
  height: 100px;
  position: relative;
  top: -50px;
  z-index: -1;
  overflow: hidden;
  transition: all 0.1s linear;
}
.footer .circlemark .topmark {
  width: 100px;
  height: 50px;
  /* position: absolute; */
  z-index: -1;
  overflow: hidden;
}
.footer .cover .top {
  width: 96px;
  height: 96px;
  z-index: -1;
  border-radius: 50%;
  border: 2px solid;
  border-color: var(--text-default-color);
}
.footer .cover .bottom {
  width: 100px;
  height: 50px;
  /* position: absolute; */
  overflow: hidden;
}
.footer .cover .bottom .bottomcircle {
  width: 96px;
  height: 96px;
  transform: translateY(-50px);
  z-index: -1;
  border-radius: 50%;
  border: 2px solid;
  border-color: var(--footer-player-bar-background-color);
}

.footer .footertime {
  padding-bottom: 15px;
  font-size: 12px;
  flex: 0;
  cursor: default;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  transition: 0.3s;
  width: 100%;
  max-width: 30vw;
}
.footer .footertime:hover {
  padding: 0;
}
.footer .footertime:hover .timeswitch {
  display: none;
}
.footer .footertime:hover .bottomprogressbar {
  display: flex;
}

.footer .left-control .detail {
  max-width: 356px;
  margin-left: 37px;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.footer .left-control .detail .ctrl {
  position: absolute;
  right: 0px;
  top: 4px;
  padding-right: 6px;
}
.footer .left-control .detail .ctrl:first-of-type .icon {
  margin-right: 5px;
}
.footer .left-control .detail .ctrl .icon {
  color: var(--text-default-color);
  opacity: 0.5;
}
.footer .left-control .detail .ctrl .icon:hover {
  opacity: 1;
}

.footer .left-control .detail .title {
  font-size: 14px;
  color: var(--text-default-color);
  min-width: 0px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin: 5px 0;
  font-size: 18px;
  font-weight: 600;
}

.footer .left-control .detail .more-info {
  margin: 5px 0;
  display: flex;
  color: var(--text-subtitle-color);
}

.footer .left-control .detail .more-info .singer {
  flex: 1;
  font-size: 12px;
  min-width: 0px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.footer .left-control .detail .more-info .singer a {
  cursor: pointer;
}

.footer .left-control .detail .more-info .current {
  width: 50px;
  font-size: 12px;
}

.footer .left-control .detail .more-info .total {
  width: 50px;
  text-align: right;
  font-size: 12px;
}
.footer .main-info .bottomprogressbar svg {
  cursor: default;
}
.footer .main-info .volume-ctrl .icon svg {
  cursor: pointer;
}
.footer .main-info .bottomprogressbar {
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  width: 100%;
  display: none;
}
.footer .main-info .playbar {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50%;
}
.footer .main-info .playbar .playbar-clickable {
  margin: 5px 10px 5px 0;
  padding: 5px 0;
  flex: 1;
  cursor: pointer;
}
.footer .main-info .barbg {
  height: 3px;
  background: var(--footer-player-bar-background-color);
}

.footer .main-info .barbg .cur {
  height: 100%;
  background: var(--footer-player-bar-cur-background-color);
  position: relative;
}
.footer .main-info .playbar .playbar-clickable:hover .cur,
.footer .main-info .m-pbar:hover .barbg .cur {
  background: var(--theme-color);
}
.footer .main-info .barbg .cur .btn {
  background: var(--footer-player-bar-cur-button-color);
  height: 8px;
  width: 2px;
  position: absolute;
  right: -2px;
  top: -5px;
  transition: 0.3s;
}

.footer .main-info .playbar .playbar-clickable:hover .barbg .cur .btn,
.footer .main-info .m-pbar:hover .barbg .cur .btn {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  top: -3px;
}

.volume-ctrl {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bottomprogressbar .icon {
  flex: 0 0 24px;
  color: var(--text-default-color);
  cursor: default;
  padding: 7px;
  display: flex;
}
.volume-ctrl .m-pbar {
  flex: 1;
  margin: 5px 0;
  padding: 5px 0;
  cursor: pointer;
}

.footer .menu-modal {
  border-radius: 10px;
  transition: 0.3s;
  left: 0;
  right: 0;
  top: 0;
  position: fixed;
  opacity: 0;
  background: var(--shadow-mask);
}
.footer .menu-modal.slideup {
  bottom: 0px;
  opacity: 1;
  transition: 0.3s;
}

.footer .menu {
  border-radius: 10px;
  position: absolute;
  z-index: 120;
  bottom: 120px;
  height: 0;
  opacity: 0;
  box-sizing: border-box;
  border: 1px solid var(--white--black);

  left: 0;
  -webkit-app-region: no-drag;
  transition: all 0.3s;
  overflow: hidden;
  width: 530px;
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  backdrop-filter: saturate(180%) blur(20px);
  background-color: var(--nav-background-color);
  box-shadow: 0px 0px 16px rgb(0 0 0 / 10%);
  padding-bottom: 20px;
}
.footer .menu.slideup {
  bottom: 125px;
  height: 500px;
  opacity: 1;
  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.footer .menu .menu-header {
  height: 30px;
  display: flex;
  align-items: center;
  color: #9e9e9e;
  padding: 30px;
  user-select: none;
}

.footer .menu .menu-header .menu-title {
  flex: 1;
  padding: 0 20px;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-default-color);
}

.footer .menu .menu-header .add-all {
  border-right: solid 1px #e5e5e5;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  padding-right: 10px;
  font-size: 14px;
}

.footer .menu .menu-header .remove-all {
  margin-left: 10px;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  font-size: 14px;
}
.footer .menu .menu-header .remove-all:hover .ng-binding,
.footer .menu .menu-header .add-all:hover .ng-binding {
  text-decoration: underline;
  color: var(--theme-color);
}
.footer .menu .menu-header .remove-all:hover,
.footer .menu .menu-header .add-all:hover {
  text-decoration: none;
  color: var(--theme-color);
}

.footer .menu .menu-header .close {
  margin-left: 15px;
  flex: 0 0 25px;
  align-items: center;
  cursor: pointer;
  color: var(--icon-default-color);
}
.footer .menu .menu-header .close:hover {
  color: var(--theme-color);
}
.footer .menu .menu-header .add-all span,
.footer .menu .menu-header .remove-all span {
  cursor: pointer;
}

.footer .menu .menu-header .add-all .icon,
.footer .menu .menu-header .remove-all .icon {
  margin-right: 7px;
  width: 18px;
  height: 18px;
}

.footer .menu .menu-header .close svg {
  margin-right: 3px;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footer .menu ul.menu-list {
  overflow-y: scroll;
  height: 370px;
  padding: 0 30px;
  font-size: 14px;
}

.footer .menu ul.menu-list li {
  border-radius: 10px;
  display: flex;
  align-items: center;
  height: 30px;
  position: relative;
  margin-bottom: -2px;
  padding: 10px 20px 10px 0;
  transition: 0.3s;
}

.footer .menu ul.menu-list li:hover {
  background: var(--songlist-hover-background-color);
}

.footer .menu ul.menu-list li.playing {
  color: var(--important-color);
  background: var(--songlist-hover-background-color);
}
.footer .menu ul.menu-list li .song-status-icon {
  flex: 0 0 30px;
  width: 20px;
  height: 30px;
  text-align: center;
  display: flex;
  align-items: center;
}
.footer .menu ul.menu-list li .song-status-icon svg {
  width: 10px;
  height: 10px;
  fill: var(--important-color);
  stroke: var(--important-color);
  flex: 1;
}
.footer .menu ul.menu-list li .song-title {
  flex: 2;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 15px;
  font-weight: 400;
  padding-right: 10px;
}
.footer .menu ul.menu-list li .song-title.disabled {
  color: #777777;
}
.footer .menu ul.menu-list li .song-title a {
  cursor: pointer;
}

.footer .menu ul.menu-list li .song-singer {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
  padding: 0 10px;
  font-weight: 300;
}

.footer .menu ul.menu-list li .tools {
  flex: 0 0 42px;
  width: 42px;
}
.footer .menu ul.menu-list li .tools .icon {
  cursor: pointer;
  opacity: 0.5;
}
.footer .menu ul.menu-list li .tools .icon:first-of-type {
  margin-right: 5px;
}
.footer .menu ul.menu-list li .tools .icon:hover {
  opacity: 1;
}

.footer .menu ul.menu-list li .song-time {
  flex: 1;
  text-align: right;
}

.footer .right-control {
  flex: 0 0 36%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.footer .right-control .playlist-toggle {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 37px;
  padding: 7px;
}
.footer .right-control .ctrl {
  display: flex;
  justify-content: center;
  align-items: center;
}
.footer .right-control .ctrl a {
  margin-right: 32px;
  padding: 7px;
  display: flex;
  transition: 0.3s;
  border-radius: 10px;
}
.footer .right-control .ctrl a:hover {
  text-decoration: none;
  background-color: var(--songlist-hover-background-color);
}
.footer .right-control .translate-switch {
  border-radius: 10px;
  display: flex;
  cursor: pointer;
  -webkit-app-region: no-drag;

  height: 0px;
  box-sizing: border-box;
  width: 0px;
  transition: 0.3s;
  overflow: hidden;
  margin: 0;
  padding: 0;
}
.footer .right-control .translate-switch.slidedown {
  padding: 7px;
  margin-right: 32px;
  height: 35px;
  box-sizing: border-box;
  width: 32px;
}
.footer .right-control .translate-switch:hover {
  background-color: var(--songlist-hover-background-color);
}
.footer .right-control .translate-switch.selected {
  color: var(--theme-color);
}
.footer .right-control .mask {
  margin-right: 32px;
  padding: 7px;
  display: flex;
  transition: 0.3s;
  border-radius: 50%;
}
.footer .right-control .mask.slidedown {
  transform: rotate(180deg);
}
.footer .right-control .mask:hover {
  background-color: var(--songlist-hover-background-color);
}

.footer .right-control .playlist-toggle .icon {
  color: var(--player-right-icon-color);
}

.footer .right-control .playlist-toggle .icon:hover {
  color: var(--player-right-icon-hover-color);
}

.footer .right-control .lyric-toggle {
  margin-right: 32px;
  padding: 7px;
  display: flex;
  cursor: pointer;
  transition: 0.3s;
  border-radius: 10px;
}
.footer .right-control .lyric-toggle:hover {
  background-color: var(--songlist-hover-background-color);
}

/* footer end */

/* dialog start */
.shadow {
  position: fixed;
  background: var(--shadow-mask);
  _position: absolute;
  z-index: 9999;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-image: url(data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==);
}

.dialog {
  position: absolute;
  top: 120px;
  width: 400px;
  height: 430px;
  z-index: 10000;
  overflow: hidden;
  border-radius: 4px;
  background-color: var(--dialog-background-color);
  color: var(--dialog-text-color);
  box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  height: 30px;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  padding: 20px;
}

.dialog-header .dialog-close {
  float: right;
  font-size: 26px;
  cursor: pointer;
  margin-top: -10px;
}

.dialog-body {
  padding: 0 20px;
  height: 370px;
  overflow-y: auto;
  background-color: var(--dialog-background-color);
}

.dialog-body .buttons {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-body .buttons button,
.dialog-body .dialog-footer button {
  margin-right: 20px;

  font-size: 16px;
  font-weight: 500;
  transition: 0.2s;
}
.dialog-body .buttons .btn-primary {
  background-color: var(--theme-color-hover);
  color: var(--theme-color);
}
.dialog-body .buttons button:hover,
.dialog-body .dialog-footer button:hover {
  transform: scale(1.2);
}
.dialog .detail-songlist li:hover {
  background-color: #e3e3e5;
  cursor: pointer;
}
.dialog-body input {
  width: calc(100% - 20px);
  height: 30px;
  border-radius: 10px;
  background: #eee;
  color: #666;
  border: transparent;
  padding: 5px 10px;
  margin-bottom: 10px;
  font-weight: 500;
}
.dialog-body input:focus {
  background-color: var(--theme-color-hover);
  color: var(--theme-color);
}

.dialog-playlist,
.dialog-backuplist,
.dialog-merge-playlist {
  padding-left: 0px;
  text-align: left;
}

.dialog-playlist li,
.dialog-backuplist li {
  cursor: pointer;
  height: 48px;
  padding: 6px;
  border-radius: 10px;
}
.dialog-merge-playlist li {
  cursor: pointer;
  height: 48px;
  padding: 10px;
  border-radius: 10px;
  transition: 0.2s;
}
.dialog-backuplist li {
  height: 96px;
  padding: 6px;
}

.dialog-playlist li:hover,
.dialog-backuplist li:hover,
.dialog-merge-playlist li:hover {
  background-color: var(--dialog-highlight-color);
}

.dialog-playlist li img,
.dialog-backuplist li img,
.dialog-merge-playlist li img {
  float: left;
  height: 48px;
  width: 48px;
  object-fit: cover;
}

.dialog-playlist li h2,
.dialog-backuplist li h2,
.dialog-merge-playlist li h2 {
  margin: 0 0 0 58px;
  font-size: 13px;
  font-weight: inherit;
}
.dialog-backuplist li h2 {
  margin-top: 0;
}
.dialog-newplaylist {
  padding: 10px;
}

.dialog-newbackup {
  text-align: center;
}

.dialog-editplaylist label,
.dialog-open-url label {
  font-size: 18px;
  font-weight: 500;
  padding: 10px 0;
  display: block;
  height: 30px;
  line-height: 30px;
}

.dialog-editplaylist .dialog-footer {
  position: absolute;
  bottom: 20px;
}
.dialog-body .field-name {
  margin: 10px 0 5px 0;
}

/* dialog end */

/* widget source-list start */
.searchbox {
  margin-bottom: 150px;
  transition: 0.3s;
}
.searchbox.footerdef {
  margin-bottom: 0;
}
.source-list {
  margin: 10px 26px 24px 26px;
  user-select: none;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.source-list .source-button {
  display: inline-block;
  cursor: pointer;
  transition: 0.1s;
}
.source-list .source-button:hover,
.source-list .source-button.active {
  transition: 0.2s;
  padding: 0;
}
.source-list .source-button a:hover {
  text-decoration: none;
}

.source-list .source-button.active .buttontext,
.source-list .source-button:hover .buttontext,
.source-list .source-button .buttontext:hover,
.source-list .source-button .buttontext.active {
  color: var(--text-default-color);
  -webkit-app-region: no-drag;
  font-size: 24px;
  font-weight: 700;
  text-decoration: none;
  border-radius: 10px;
  padding: 6px 10px;
  transition: all 0.2s, background 0.3s;
  -webkit-user-drag: none;
  margin: 0px 12px;
  white-space: nowrap;
}
.source-list .source-button.active .buttontext,
.source-list .source-button .buttontext.active {
  border-bottom: solid 2px var(--text-default-color);
}
.source-list .source-button:hover .buttontext,
.source-list .source-button .buttontext:hover {
  background-color: var(--button-background-color);
}
.source-list .source-button .buttontext {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  font-size: 14px;
  margin: 4px 10px;
  color: var(--text-subtitle-color);
  transition: 0.1s;
}

.source-list .splitter {
  display: inline-block;
  background: #a9a9a9;
  margin-top: 1px;
  height: 12px;
  width: 1px;
}
.source-list .search-type {
  float: right;
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  justify-content: flex-end;
}
/* widget source-list end */

/* widget playlist-filter start */

.playlist-filter {
  display: flex;
  flex-wrap: wrap;
  line-height: 38px;
  margin: 0 26px 0px 26px;
}

.playlist-filter .filter-item {
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 20px;
  padding: 8px 16px;
  margin: 10px 16px 6px 0;
  color: var(--black--white);
  font-weight: 600;
  font-size: 16px;
  border-radius: 10px;
  transition: all 0.2s;
}

.playlist-filter .filter-item.active,
.playlist-filter .filter-item:hover {
  background: var(--theme-color-hover);
  color: var(--theme-color);
  transition: all 0.2s;
}

/* widget playlist-filter end */

/* widget all-playlist-filter start */
.all-playlist-filter {
  margin: 10px 26px 0px 26px;
  border-radius: 10px;
  padding: 8px;
  background-color: var(--button-background-color);
}

.all-playlist-filter .category {
  margin-bottom: 32px;
  display: flex;
}

.all-playlist-filter .category .category-title {
  font-size: 24px;
  font-weight: 700;
  opacity: 0.68;
  margin-left: 24px;
  min-width: 54px;
  height: 26px;
  margin-top: 8px;
  /* color: var(--icon-default-color); */
}
.all-playlist-filter .category .category-filters {
  margin-left: 24px;
  display: flex;
  flex-wrap: wrap;
  /* color: var(--icon-default-color); */
}
.all-playlist-filter .category .category-filters .filter-item {
  min-width: 98px;
  margin-top: 4px;
  display: flex;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  transition: 0.2s;
}
.all-playlist-filter .category .category-filters .filter-item span {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  padding: 6px 10px;
  height: 26px;
  border-radius: 12px;
  opacity: 0.88;
  transition: 0.2s;
}
.all-playlist-filter .category .category-filters .filter-item span:hover {
  opacity: 1;
  background-color: var(--theme-color-hover);
  border-radius: var(--default-border-radius);
  transition: 0.2s;
  color: var(--theme-color);
}
.all-playlist-filter .category .category-filters .filter-item span.active {
  color: var(--theme-color);
}

/* widget all-playlist-filter end */