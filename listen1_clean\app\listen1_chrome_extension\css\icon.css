@font-face {
  font-family: 'listen1-icon';
  src: url('../fonts/listen1-icon.eot?4ftssm');
  src: url('../fonts/listen1-icon.eot?4ftssm#iefix') format('embedded-opentype'),
    url('../fonts/listen1-icon.ttf?4ftssm') format('truetype'),
    url('../fonts/listen1-icon.woff?4ftssm') format('woff'),
    url('../fonts/listen1-icon.svg?4ftssm#listen1-icon') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^='li-'],
[class*=' li-'] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'listen1-icon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.li-play:before {
  content: '\e900';
}
.li-previous:before {
  content: '\e901';
}
.li-next:before {
  content: '\e902';
}
.li-pause:before {
  content: '\e903';
}
.li-random-loop:before {
  content: '\e904';
}
.li-single-cycle:before {
  content: '\e905';
}
.li-mute:before {
  content: '\e906';
}
.li-volume:before {
  content: '\e907';
}
.li-list:before {
  content: '\e908';
}
.li-loop:before {
  content: '\e909';
}
.li-del:before {
  content: '\e90a';
}
.li-close:before {
  content: '\e90b';
}
.li-back:before {
  content: '\e90c';
}
.li-play-s:before {
  content: '\e90d';
}
.li-collapse:before {
  content: '\e90e';
}
.li-add:before {
  content: '\e90f';
}
.li-advance:before {
  content: '\e910';
}
.li-link:before {
  content: '\e911';
}
.li-setting:before {
  content: '\e912';
}
.li-songlist:before {
  content: '\e913';
}
.li-featured-list:before {
  content: '\e914';
}
