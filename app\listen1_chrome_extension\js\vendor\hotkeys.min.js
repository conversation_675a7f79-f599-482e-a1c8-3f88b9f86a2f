/*! hotkeys-js v3.8.3 | MIT (c) 2021 kenny wong <<EMAIL>> | http://jaywcjlove.github.io/hotkeys */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).hotkeys=t()}(this,function(){"use strict";var e="undefined"!=typeof navigator&&0<navigator.userAgent.toLowerCase().indexOf("firefox");function u(e,t,n){e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent&&e.attachEvent("on".concat(t),function(){n(window.event)})}function p(e,t){for(var n=t.slice(0,t.length-1),o=0;o<n.length;o++)n[o]=e[n[o].toLowerCase()];return n}function d(e){"string"!=typeof e&&(e="");for(var t=(e=e.replace(/\s/g,"")).split(","),n=t.lastIndexOf("");0<=n;)t[n-1]+=",",t.splice(n,1),n=t.lastIndexOf("");return t}for(var t={backspace:8,tab:9,clear:12,enter:13,return:13,esc:27,escape:27,space:32,left:37,up:38,right:39,down:40,del:46,delete:46,ins:45,insert:45,home:36,end:35,pageup:33,pagedown:34,capslock:20,num_0:96,num_1:97,num_2:98,num_3:99,num_4:100,num_5:101,num_6:102,num_7:103,num_8:104,num_9:105,num_multiply:106,num_add:107,num_enter:108,num_subtract:109,num_decimal:110,num_divide:111,"\u21ea":20,",":188,".":190,"/":191,"`":192,"-":e?173:189,"=":e?61:187,";":e?59:186,"'":222,"[":219,"]":221,"\\":220},y={"\u21e7":16,shift:16,"\u2325":18,alt:18,option:18,"\u2303":17,ctrl:17,control:17,"\u2318":91,cmd:91,command:91},h={16:"shiftKey",18:"altKey",17:"ctrlKey",91:"metaKey",shiftKey:16,ctrlKey:17,altKey:18,metaKey:91},m={16:!1,18:!1,17:!1,91:!1},v={},n=1;n<20;n++)t["f".concat(n)]=111+n;var g=[],o="all",w=[],k=function(e){return t[e.toLowerCase()]||y[e.toLowerCase()]||e.toUpperCase().charCodeAt(0)};function r(e){o=e||"all"}function O(){return o||"all"}function a(e){var a=e.scope,f=e.method,t=e.splitKey,c=void 0===t?"+":t;d(e.key).forEach(function(e){var t=e.split(c),n=t.length,o=t[n-1],i="*"===o?"*":k(o);if(v[i]){a=a||O();var r=1<n?p(y,t):[];v[i]=v[i].map(function(e){return f&&e.method!==f||e.scope!==a||!function(e,t){for(var n=e.length<t.length?t:e,o=e.length<t.length?e:t,i=!0,r=0;r<n.length;r++)~o.indexOf(n[r])||(i=!1);return i}(e.mods,r)?e:{}})}})}function K(e,t,n){var o;if(t.scope===n||"all"===t.scope){for(var i in o=0<t.mods.length,m)Object.prototype.hasOwnProperty.call(m,i)&&(!m[i]&&~t.mods.indexOf(+i)||m[i]&&!~t.mods.indexOf(+i))&&(o=!1);(0!==t.mods.length||m[16]||m[18]||m[17]||m[91])&&!o&&"*"!==t.shortcut||!1===t.method(e,t)&&(e.preventDefault?e.preventDefault():e.returnValue=!1,e.stopPropagation&&e.stopPropagation(),e.cancelBubble&&(e.cancelBubble=!0))}}function b(n){var e=v["*"],t=n.keyCode||n.which||n.charCode;if(x.filter.call(this,n)){if(93!==t&&224!==t||(t=91),~g.indexOf(t)||229===t||g.push(t),["ctrlKey","altKey","shiftKey","metaKey"].forEach(function(e){var t=h[e];n[e]&&!~g.indexOf(t)?g.push(t):!n[e]&&~g.indexOf(t)?g.splice(g.indexOf(t),1):"metaKey"===e&&n[e]&&3===g.length&&(n.ctrlKey||n.shiftKey||n.altKey||(g=g.slice(g.indexOf(t))))}),t in m){for(var o in m[t]=!0,y)y[o]===t&&(x[o]=!0);if(!e)return}for(var i in m)Object.prototype.hasOwnProperty.call(m,i)&&(m[i]=n[h[i]]);n.getModifierState&&(!n.altKey||n.ctrlKey)&&n.getModifierState("AltGraph")&&(~g.indexOf(17)||g.push(17),~g.indexOf(18)||g.push(18),m[17]=!0,m[18]=!0);var r=O();if(e)for(var a=0;a<e.length;a++)e[a].scope===r&&("keydown"===n.type&&e[a].keydown||"keyup"===n.type&&e[a].keyup)&&K(n,e[a],r);if(t in v)for(var f=0;f<v[t].length;f++)if(("keydown"===n.type&&v[t][f].keydown||"keyup"===n.type&&v[t][f].keyup)&&v[t][f].key){for(var c=v[t][f],l=c.key.split(c.splitKey),s=[],u=0;u<l.length;u++)s.push(k(l[u]));s.sort().join("")===g.sort().join("")&&K(n,c,r)}}}function x(e,t,n){g=[];var o=d(e),i=[],r="all",a=document,f=0,c=!1,l=!0,s="+";for(void 0===n&&"function"==typeof t&&(n=t),"[object Object]"===Object.prototype.toString.call(t)&&(t.scope&&(r=t.scope),t.element&&(a=t.element),t.keyup&&(c=t.keyup),void 0!==t.keydown&&(l=t.keydown),"string"==typeof t.splitKey&&(s=t.splitKey)),"string"==typeof t&&(r=t);f<o.length;f++)i=[],1<(e=o[f].split(s)).length&&(i=p(y,e)),(e="*"===(e=e[e.length-1])?"*":k(e))in v||(v[e]=[]),v[e].push({keyup:c,keydown:l,scope:r,mods:i,shortcut:o[f],method:n,key:o[f],splitKey:s});void 0!==a&&!~w.indexOf(a)&&window&&(w.push(a),u(a,"keydown",function(e){b(e)}),u(window,"focus",function(){g=[]}),u(a,"keyup",function(e){b(e),function(e){var t=e.keyCode||e.which||e.charCode,n=g.indexOf(t);if(n<0||g.splice(n,1),e.key&&"meta"==e.key.toLowerCase()&&g.splice(0,g.length),93!==t&&224!==t||(t=91),t in m)for(var o in m[t]=!1,y)y[o]===t&&(x[o]=!1)}(e)}))}var i={setScope:r,getScope:O,deleteScope:function(e,t){var n,o;for(var i in e=e||O(),v)if(Object.prototype.hasOwnProperty.call(v,i))for(n=v[i],o=0;o<n.length;)n[o].scope===e?n.splice(o,1):o++;O()===e&&r(t||"all")},getPressedKeyCodes:function(){return g.slice(0)},isPressed:function(e){return"string"==typeof e&&(e=k(e)),!!~g.indexOf(e)},filter:function(e){var t=e.target||e.srcElement,n=t.tagName,o=!0;return!t.isContentEditable&&("INPUT"!==n&&"TEXTAREA"!==n&&"SELECT"!==n||t.readOnly)||(o=!1),o},unbind:function(e){if(e){if(Array.isArray(e))e.forEach(function(e){e.key&&a(e)});else if("object"==typeof e)e.key&&a(e);else if("string"==typeof e){for(var t=arguments.length,n=Array(1<t?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];var i=n[0],r=n[1];"function"==typeof i&&(r=i,i=""),a({key:e,scope:i,method:r,splitKey:"+"})}}else Object.keys(v).forEach(function(e){return delete v[e]})}};for(var f in i)Object.prototype.hasOwnProperty.call(i,f)&&(x[f]=i[f]);if("undefined"!=typeof window){var c=window.hotkeys;x.noConflict=function(e){return e&&window.hotkeys===x&&(window.hotkeys=c),x},window.hotkeys=x}return x});