html,
body {
  margin: 0;
  padding: 0;
  font-size: var(--text-default-size);
  color: var(--text-default-color);
  font-family: system-ui, 'PingFang SC', STHeiti, sans-serif;
}

a {
  cursor: pointer;
}

.wrap {
  /* https://stackoverflow.com/questions/28897089/z-index-on-borders */
  outline: solid 1px var(--windows-border-color);
  box-sizing: border-box;
}

/* remove focus highlight */
input:focus,
select:focus,
textarea:focus,
button:focus {
  outline: none;
}

ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

input,
svg,
.icon {
  -webkit-app-region: no-drag;
}

button {
  background-color: var(--button-background-color);
  color: var(--text-default-color);
  cursor: pointer;
  border: solid 1px var(--button-background-color);
  border-radius: var(--default-border-radius);
  padding: 5px;
  min-width: 80px;
  min-height: 32px;
}
button:hover {
  background-color: var(--button-hover-background-color);
}
img {
  -webkit-user-drag: none;
}
.l1-button {
  background-color: var(--button-background-color);
  color: var(--text-default-color);
  border-radius: var(--default-border-radius);
  padding: 5px;
  margin-right: 4px;
  color: var(--text-default-color);
  cursor: pointer;
  display: inline-block;
}
.l1-button:hover {
  background: var(--button-hover-background-color);
}
svg {
  width: 24px;
  height: 24px;
  stroke: currentColor;
  stroke-width: 1;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
  cursor: pointer;
  /*    stroke: var(--icon-default-color);*/
}

/* svg:hover {
  fill: var(--icon-highlight-color);
  stroke: var(--icon-highlight-color);
} */

.icon {
  /* default icon settings */
  font-size: 16px;
  cursor: pointer;
}

/* tools utils */
.flex-scroll-wrapper {
  flex: 1;
  height: 100px;
  overflow-y: scroll;
  scrollbar-width: thin;
  scrollbar-color: var(--scroll-color) var(--content-background-color);
}

/* scroll bar style */
::-webkit-scrollbar {
  width: 14px;
  height: 18px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  height: 49px;
  border: 5px solid rgba(0, 0, 0, 0);
  background-clip: padding-box;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  background-color: var(--scroll-color);
  /*rgba(151, 151, 151, 0.4);*/

  /*    -webkit-box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05), inset 1px 1px 0px rgba(0, 0, 0, 0.05);*/
}

::-webkit-scrollbar-button {
  width: 0;
  height: 0;
  display: none;
}

::-webkit-scrollbar-corner {
  background-color: transparent;
}

/* main framework start */
.wrap {
  display: flex;
  height: 100vh;
  flex-direction: column;
  margin: auto;
}

/* split screen to up/down 2 parts */
.main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.footer {
  background: var(--foot-background-color);
  height: 60px;
  border-top: solid 1px var(--line-default-color);
  display: flex;
  position: relative;
  z-index: 99;
}

/* split main to left/right 2 parts */
.main .sidebar {
  flex: 0 0 200px;
  display: flex;
  flex-direction: column;
  background: var(--sidebar-background-color);
}

.main .content {
  background: var(--content-background-color);
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* split content to up/down 2 parts */
.main .content .navigation {
  height: 46px;
  flex: 0 0 46px;
  border-bottom: solid 1px var(--line-default-color);
  display: flex;
  align-items: center;
  -webkit-app-region: drag;
}

.main .content .browser {
  flex: 1;
}

/* main framework end */

/*****************************************************************/

/* main sidebar start */
.sidebar .menu-control {
  height: 43px;
  width: 125px;
  -webkit-app-region: drag;
}
.sidebar .menu-title {
  height: 28px;
  line-height: 28px;
  margin: 0 12px 4px 12px;
  color: var(--link-default-color);
  padding-left: 10px;
  display: flex;
  align-items: center;
  font-size: 12px;
}
.sidebar .menu-title .title {
  flex: 1;
}
.sidebar .menu-title svg {
  flex: 0 0 18px;
}

.sidebar ul li {
  cursor: pointer;
  padding-left: 10px;
  border-top: solid 2px transparent;
  border-bottom: solid 2px transparent;
  margin-bottom: -2px;
}
.sidebar ul li .sidebar-block {
  display: flex;
  align-items: center;
  line-height: 28px;
  padding-left: 12px;
  margin: 3px 0;
  color: var(--text-default-color);

  border-radius: var(--default-border-radius);
}

.sidebar svg {
  width: 18px;
  height: 18px;
}

.sidebar ul li a {
  margin-left: 10px;
  width: 125px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.sidebar ul li:hover .sidebar-block {
  background: var(--sidebar-hover-background-color);
  color: var(--sidebar-hover-text-color);
}

.sidebar ul li.active .sidebar-block,
.sidebar ul li.active:hover .sidebar-block {
  background: var(--sidebar-highlight-background-color);
  color: var(--sidebar-highlight-text-color);
}
.sidebar ul li.dragover .sidebar-block {
  background: var(--sidebar-highlight-background-color);
  color: var(--sidebar-highlight-text-color);
}
/*
avoid hover effect trigger dragleave event
https://stackoverflow.com/questions/19889615/can-an-angular-directive-pass-arguments-to-functions-in-expressions-specified-in
*/
.sidebar ul li * {
  pointer-events: none;
}
/* main sidebar end */

/* widget navigation start */
.navigation svg {
  width: 18px;
  height: 18px;
  color: var(--icon-default-color);
}
.navigation .icon svg {
  color: var(--text-default-color);
}
.navigation .backfront {
  flex: 0 0 45px;
  line-height: 46px;
  vertical-align: middle;
  padding: 0 13px;
}

.navigation .search {
  flex: 1;
}

.navigation .settings {
  flex: 0 0 32px;
}

.navigation .icon {
  color: var(--text-default-color);
  opacity: 0.5;
}

.navigation .icon:hover {
  opacity: 1;
}

.navigation .backfront .icon {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 4px;
}

.navigation .backfront .icon:nth-of-type(1) {
  margin-right: 8px;
}

.navigation .search-input {
  width: 270px;
  height: 23px;
  background: var(--search-input-background-color);
  border-style: none;
  border-radius: var(--default-border-radius);
  padding-left: 10px;
  font-size: 12px;
  color: var(--text-default-color);
}

.navigation .window-control {
  flex: 0 0 105px;
  border-left: solid 1px var(--window-control-border-color);
  margin-left: 15px;
}

.navigation .window-control svg {
  margin-left: 8px;
}

.navigation .window-control svg:first-of-type {
  margin-left: 15px;
}

/* navigation end */

/* page hot-playlist start */
.page-hot-playlist {
  max-width: 850px;
  margin: 0 auto;
}

.playlist-covers {
  margin: 0;
  padding: 0 13px;
  display: flex;
  flex-flow: row wrap;
  position: relative;
}

.playlist-covers li {
  flex: 0 1 calc(20% - 26px);
  min-height: 156px;
  color: var(--text-default-color);
  margin: 0 13px;
}

.playlist-covers .u-cover {
  display: flex;
  position: relative;
}

.playlist-covers .u-cover img {
  height: 136px;
  min-width: 136px;
  max-width: 100%;
  object-fit: cover;
  margin: auto;
  border: solid 1px var(--line-default-color);
  margin-bottom: 2px;
  cursor: pointer;
}

.playlist-covers .u-cover .bottom {
  position: absolute;
  right: 5px;
  bottom: 10px;
  height: 30px;
  width: 30px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s linear;
}

.playlist-covers .u-cover:hover .bottom {
  opacity: 1;
}

.playlist-covers .u-cover .bottom svg {
  height: 30px;
  width: 30px;
  fill: rgba(200, 200, 200, 0.5);
  stroke-width: 1;
  stroke: #ffffff;
}

.playlist-covers .u-cover .bottom svg:hover {
  fill: rgba(100, 100, 100, 0.5);
}

.playlist-covers .desc {
  cursor: pointer;
}

.playlist-covers .desc .title {
  display: flex;
  min-height: 32px;
  margin: 0 0 5px;
}
/* page hot-playlist end */

/* page playlist-detail start */
.page .playlist-detail {
  padding-bottom: 37px;
}

.page .playlist-detail .detail-head {
  display: flex;
}

.page .playlist-detail .detail-head img {
  height: 150px;
}

.page .playlist-detail .detail-head .detail-head-cover {
  flex: 0 0 150px;
  padding: 26px 26px 8px 26px;
}

.page .playlist-detail .detail-head .detail-head-title {
  flex: 1;
}

.playlist-button-list {
  display: flex;
  flex-flow: row wrap;
}

.playlist-button-list .playlist-button {
  height: 26px;
  border: solid 1px var(--button-border-color);
  cursor: pointer;
  border-radius: 2px;
  display: flex;
  margin: 0 20px 20px 0;
}

.playlist-button-list .playlist-button.playadd-button {
  flex: 0 0 136px;
}

.playlist-button-list .playlist-button .play-list {
  flex: 1;
  padding: 0 18px;
  display: flex;
  align-items: center;
}
.playlist-button-list .playlist-button .play-list svg {
  margin-right: 4px;
}

.playlist-button-list .playlist-button.playadd-button .play-list svg {
  width: 14px;
  height: 14px;
  flex: 0 0 14px;
  margin-right: 4px;
  stroke: var(--important-color);
  fill: var(--important-color);
}
.playlist-button-list .playlist-button .play-list .icon {
  margin-right: 8px;
}
.playlist-button-list .playlist-button.playadd-button .play-list .icon {
  flex: 0 0 14px;
  margin-right: 4px;
  color: var(--important-color);
}

.playlist-button-list .playlist-button.playadd-button .add-list {
  flex: 0 0 26px;
  height: 26px;
  width: 26px;
  border-left: solid 1px var(--button-border-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.playlist-button-list .playlist-button.edit-button .play-list.favorited {
  color: var(--text-default-color);
}
.playlist-button-list .playlist-button.edit-button .play-list.notfavorite {
  color: var(--text-default-color);
}

.playlist-button-list .playlist-button .play-list:hover,
.playlist-button-list .playlist-button.playadd-button .add-list:hover {
  background: var(--button-hover-background-color);
}
.playlist-button-list .playlist-button.playadd-button .add-list svg {
  width: 14px;
  height: 14px;
}

.playlist-button-list .playlist-button.clone-button,
.playlist-button-list .playlist-button.edit-button,
.playlist-button-list .playlist-button.fav-button {
  flex: 0 0 auto;
}

.playlist-button-list .playlist-button.clone-button .play-list svg,
.playlist-button-list .playlist-button.edit-button .play-list svg,
.playlist-button-list .playlist-button.fav-button .play-list svg {
  width: 16px;
  height: 16px;
  flex: 0 0 16px;
  margin-right: 8px;
  stroke: rgb(102, 102, 102);
}

.playlist-button-list .playlist-button.fav-button .play-list.favorited svg {
  fill: rgb(102, 102, 102);
}

.page .playlist-detail .detail-head .detail-head-title h2 {
  font-size: var(--h2-title-font-size);
}
/* page playlist detail end */

/* page song detail start */
.page .songdetail-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 60px;
  background: var(--now-playing-page-background-color);
  overflow: hidden;
  border: solid 1px var(--windows-border-color);
  -webkit-app-region: no-drag;
  transition: all 0.3s;
}

.page .songdetail-wrapper .draggable-zone {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  -webkit-app-region: drag;
  height: 80px;
}

.page .songdetail-wrapper.slidedown .draggable-zone {
  display: none;
  -webkit-app-region: no-drag;
}

.page .songdetail-wrapper .translate-switch {
  border: solid 1px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 30px;
  right: 30px;
  color: #888888;
  cursor: pointer;
  -webkit-app-region: no-drag;
}
.page .songdetail-wrapper .translate-switch:hover {
  color: var(--text-default-color);
}
.page .songdetail-wrapper .translate-switch.selected {
  color: var(--text-default-color);
}
.page .songdetail-wrapper.slidedown {
  top: calc(100% - 60px);
}

.page .songdetail-wrapper .close {
  position: absolute;
  top: 24px;
  left: 24px;
  height: 24px;
  width: 24px;
  cursor: pointer;
  -webkit-app-region: no-drag;
}
.page .songdetail-wrapper .close.mac {
  top: 44px;
}

.page .songdetail-wrapper .window-control {
  position: absolute;
  top: 24px;
  right: 24px;
  height: 24px;
  cursor: pointer;
  -webkit-app-region: no-drag;
  z-index: 99;
}

.page .songdetail-wrapper .window-control svg {
  margin-left: 8px;
  stroke: var(--now-playing-close-icon-color);
}

.page .songdetail-wrapper .close svg {
  stroke: var(--now-playing-close-icon-color);
}

.page .bg {
  opacity: 0.5;
  height: 100%;
  text-align: center;
  line-height: 100%;
  float: left;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  filter: blur(10px) brightness(0.6);
  transition: background ease-in-out 1.5s;
}

.page .playsong-detail {
  position: absolute;
  left: 10px;
  right: 10px;
  max-width: 770px;
  margin: 0 auto;
  display: flex;
  height: 100%;
}

.page .playsong-detail .detail-head {
  flex: 0 0 350px;
  overflow: hidden;
}

.page .playsong-detail .detail-head .detail-head-cover {
  width: 250px;
  height: 250px;
  margin-top: 110px;
}

.page .playsong-detail .detail-head img {
  width: 250px;
  height: 250px;
  object-fit: cover;
}

.page .playsong-detail .detail-songinfo {
  flex: 1;
  margin-top: 80px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  -webkit-app-region: no-drag;
}
.page .playsong-detail .detail-songinfo .title {
  display: flex;
  align-items: center;
}
.page .playsong-detail .detail-songinfo .title h2 {
  font-size: var(--h2-title-font-size);
  font-weight: 400;
}
.page .playsong-detail .detail-songinfo .title .badge {
  font-size: var(--badge-font-size);
  color: var(--badge-font-color);
  border: solid 1px var(--badge-border-color);
  border-radius: 3px;
  margin-left: 5px;
  padding-left: 4px;
  padding-right: 4px;
  margin-top: 4px;
  box-sizing: border-box;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}
.page .playsong-detail .detail-songinfo .title .badge.platform {
  padding-top: 1px;
}
.page .playsong-detail .detail-songinfo .title .badge:first-of-type {
  margin-left: 15px;
}
.page .playsong-detail .detail-songinfo .info {
  border-bottom: solid 1px var(--line-default-color);
  padding-bottom: 6px;
  flex: 0 0 20px;
  display: flex;
}
.page .playsong-detail .detail-songinfo .info a {
  cursor: pointer;
}
.page .playsong-detail .detail-songinfo .info .singer {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.page .playsong-detail .detail-songinfo .info .album {
  flex: 2;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.page .playsong-detail .detail-songinfo .info span {
  color: var(--lyric-default-color);
}
.page .coverbg .playsong-detail .detail-songinfo .info span {
  color: var(--lyric-on-cover-color);
}
.page .playsong-detail .detail-songinfo .lyric {
  position: relative;
  flex: 0 0 380px;
  overflow-y: scroll;
  color: var(--lyric-default-color);
  scrollbar-width: thin;
  scrollbar-color: var(--scroll-color) transparent;
  font-size: var(--lyric-font-size);
}
.page .coverbg .playsong-detail .detail-songinfo .lyric {
  color: var(--lyric-on-cover-color);
}
.page .playsong-detail .detail-songinfo .lyric p {
  margin: var(--lyric-line-margin) 0 0 0;
}
.page .playsong-detail .detail-songinfo .lyric p.translate {
  margin: 5px 0 0 0;
}
.page .playsong-detail .detail-songinfo .lyric p.hide {
  display: none;
}
.page .playsong-detail .detail-songinfo .lyric p.highlight {
  color: var(--lyric-important-color);
}
.page .coverbg .playsong-detail .detail-songinfo .lyric p.highlight {
  color: var(--lyric-important-on-cover-color);
}

ul.detail-songlist {
  padding: 0 25px;
  position: relative;
}

ul.detail-songlist .playlist-search {
  position: absolute;
  right: 0;
  top: -30px;
}
ul.detail-songlist .playlist-search .playlist-search-icon {
  width: 14px;
  position: absolute;
  left: 7px;
  top: 1px;
}
ul.detail-songlist .playlist-search .playlist-clear-icon {
  width: 14px;
  position: absolute;
  left: 158px;
}
ul.detail-songlist .playlist-search .playlist-search-input {
  margin-right: 28px;
  margin-bottom: 10px;
  border: none;
  height: 24px;
  border-radius: 12px;
  padding: 0 30px;
  background: var(--content-background-color);
  color: #bbbbbb;
  width: 120px;
}
ul.detail-songlist .playlist-search .playlist-search-input:hover {
  background-color: var(--songlist-odd-background-color);
}
ul.detail-songlist .playlist-search .playlist-search-input::placeholder {
  color: #bbbbbb;
}

ul.detail-songlist li {
  /* https://stackoverflow.com/questions/4157005/css-positioning-z-index-negative-margins */
  position: relative;
  display: flex;
  border-top: solid 2px var(--songlist-border-color);
  border-bottom: solid 2px var(--songlist-border-color);
  height: 37px;
  align-items: center;
  padding: 0 20px;
  font-size: 14px;
  margin-bottom: -2px;
}

ul.detail-songlist li.playlist-result {
  height: 80px;
  padding: 0 10px;
}
ul.detail-songlist li.odd {
  background-color: var(--songlist-odd-background-color);
}

ul.detail-songlist li:hover,
ul.detail-songlist li.odd:hover {
  background-color: var(--songlist-hover-background-color);
}

ul.detail-songlist li a {
  cursor: pointer;
}
ul.detail-songlist li a.disabled {
  color: var(--disable-song-title-color);
}
ul.detail-songlist li a span.source {
  border: solid 1px #ccc;
  border-radius: 4px;
  margin-right: 10px;
  display: inline-block;
  padding: 0 4px;
  color: #ccc;
  font-size: 12px;
  width: 24px;
  text-align: center;
}
ul.detail-songlist li a span.source.playlist {
  margin-left: 10px;
  margin-right: 0;
}
ul.detail-songlist li.head {
  height: 28px;
  color: var(--text-disable-color);
  border-top: none;
  padding-bottom: 2px;
}
ul.detail-songlist li.head:hover {
  background-color: transparent;
}

ul.detail-songlist li .title {
  flex: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-height: 17px;
  max-height: 38px;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
ul.detail-songlist li.playlist-result .title {
  max-height: 80px;
}

ul.detail-songlist li.playlist-result .title a {
  display: flex;
  align-items: center;
}

ul.detail-songlist li.playlist-result .title img {
  height: 60px;
  width: 60px;
  display: block;
  margin-right: 10px;
}

ul.detail-songlist li .artist {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-height: 17px;
  max-height: 38px;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

ul.detail-songlist li .album {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-height: 17px;
  max-height: 38px;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

ul.detail-songlist li .tools {
  flex: 0 0 110px;
  display: flex;
  align-items: center;
}
ul.detail-songlist li .tools .icon {
  height: 16px;
  width: 16px;
  color: #9d9d9d;
  margin-top: 2px;
  margin-right: 10px;
}
/* page song detail end */

/* page login start */
.page .login {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 192px);
}
.page .login .login-logo {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}
.page .login .login-logo img {
  height: 64px;
  margin: 20px;
}
.page .login .login-title {
  font-size: 18px;
  margin-bottom: 10px;
}
.page .login .login-form .login-form_field {
  display: flex;
  align-items: center;
  height: 40px;
  margin: 24px;
  width: 270px;
  border: solid 1px var(--button-background-color);
}
.page .login .login-form .login-form_field input {
  background: var(--content-background-color);
  color: var(--text-default-color);
}
.page .login .login-form .login-form_field input.login-form_field_countrycode {
  flex: 0 0 40px;
  width: 40px;
}
.page .login .login-form .login-form_field svg {
  margin-left: 12px;
  margin-right: 12px;
  color: var(--icon-default-color);
  width: 18px;
  height: 18px;
}
.page .login .login-form .login-form_field input {
  border: none;
  flex: 1;
  font-size: 16px;
}
.page .login .login-submit_button {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-top: 24px;
  padding: 8px;
  width: 270px;
  cursor: pointer;
  border: solid 1px var(--button-border-color);
}
.page .login .login-switcher {
  margin-top: 24px;
  cursor: pointer;
}
.page .login .login-notice {
  width: 270px;
  border-top: 1px solid var(--button-border-color);
  margin-top: 30px;
  padding-top: 12px;
  font-size: 12px;
  color: var(--text-subtitle-color);
}
.page .login .usercard {
  display: flex;
  align-items: center;
  width: 400px;
  border: solid 1px var(--button-border-color);
  margin-bottom: 20px;
}
.page .login .usercard img {
  width: 60px;
  height: 60px;
  margin: 10px;
}
.page .login .usercard .usercard-title {
  flex: 1;
  height: 50px;
  font-size: 16px;
  font-weight: 700;
}
.page .login .usercard .usercard-title .usercard-info {
  color: var(--link-inactive-color);
  font-size: 12px;
}
.page .login .usercard button {
  margin: 10px;
}
/* page login end */

/* page setting start */
.page .settings-title {
  border-bottom: solid 1px var(--line-default-color);
  padding-bottom: 10px;
  max-width: 800px;
  margin: 0 25px;
  font-size: 17px;
  margin-bottom: 10px;
}
.page .settings-title:first-of-type {
  margin-top: 20px;
}
.page .settings-content {
  margin: 0 25px 25px 25px;
}
.page .settings-content label.upload-button,
.page .settings-content .language-button {
  padding: 5px;
  background: var(--button-background-color);
  margin-right: 4px;
  color: var(--text-default-color);
  cursor: pointer;
}

.page .settings-content label.upload-button:hover,
.page .settings-content .language-button:hover {
  background: var(--button-hover-background-color);
}
.page .settings-content .shortcut {
  display: flex;
  margin-top: 10px;
}
.page .settings-content .shortcut svg {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}
.page .searchbox .search-pagination {
  text-align: center;
  padding: 15px;
}
.page .settings-content .shortcut_table .shortcut_table-header,
.page .settings-content .shortcut_table .shortcut_table-line {
  display: flex;
  color: var(--text-default-color);
  box-sizing: border-box;
  align-items: center;
  height: 40px;
}
.page .settings-content .shortcut_table .shortcut_table-header {
  color: var(--link-default-color);
  height: 30px;
}
.page .settings-content .shortcut_table .shortcut_table-function {
  flex: 0 140px;
  padding: 0 10px;
  box-sizing: border-box;
}
.page .settings-content .shortcut_table .shortcut_table-key {
  flex: 0 200px;
  margin-right: 20px;
  box-sizing: border-box;
}
.page .settings-content .shortcut_table .shortcut_table-globalkey {
  flex: 0 240px;
  box-sizing: border-box;
}
.page
  .settings-content
  .shortcut_table
  .shortcut_table-line
  .shortcut_table-key {
  border: solid 1px var(--button-border-color);
  border-radius: 5px;
  padding: 0 10px;
  height: 30px;
  display: flex;
  align-items: center;
}
.page
  .settings-content
  .shortcut_table
  .shortcut_table-line
  .shortcut_table-globalkey {
  border: solid 1px var(--button-border-color);
  border-radius: 5px;
  height: 30px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.page .settings-content .custom-proxy {
  margin-top: 10px;
}
.page .settings-content .custom-proxy .rule-input {
  margin-top: 8px;
}
.page .settings-content .custom-proxy input {
  margin-right: 15px;
  height: 24px;
  width: 200px;
}
.page .settings-content .search-description {
  margin: 10px 0 5px 0;
}
.page .settings-content .search-source-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  line-height: 30px;
}
.page .settings-content .search-source-list .search-source {
  display: flex;
  align-items: center;
  width: 130px;
}
.page .settings-content .search-source-list .search-source svg {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}
/* page setting end */

.loading_bottom {
  display: block;
  width: 40px;
  margin: 0 auto;
}

svg.searchspinner {
  width: 20px;
  height: 20px;
  vertical-align: top;
  margin-left: 15px;
}
/* footer start */

.footer {
  background: var(--foot-background-color);
  height: 60px;
  border-top: solid 1px var(--foot-border-color);
  display: flex;
  position: relative;
}

.footer .left-control {
  flex: 0 0 300px;
  display: flex;
  align-items: center;
}

.footer .left-control .icon {
  font-size: 22px;
  color: var(--player-left-icon-color);
  margin: 0 13px;
}

.footer .left-control .icon.play {
  margin-right: 10px;
}

.footer .left-control .icon:first-of-type {
  margin-left: 42px;
}

.footer .left-control .icon.play {
  color: var(--player-icon-color);
}
.footer .left-control .icon.play:hover {
  color: var(--player-icon-hover-color);
}

.footer .main-info {
  flex: 1;
  background: var(--footer-main-background-color);
  display: flex;
  overflow: hidden;
  z-index: 1;
}

.footer .main-info .logo-banner {
  text-align: center;
  flex: 1;
  display: flex;
  align-items: center;
}

.footer .main-info .logo-banner svg.logo {
  height: 48px;
  width: 48px;
  fill: #666666;
  stroke: #666666;
  margin: 0 auto;
}

.footer .main-info .cover {
  height: 60px;
  width: 60px;
  object-fit: cover;
  flex: 0 0 60px;
  cursor: pointer;
  position: relative;
  color: #ffffff;
}
.footer .main-info .cover img {
  height: 60px;
  width: 60px;
  object-fit: cover;
}
.footer .main-info .cover .mask {
  display: none;
}
.footer .main-info .cover:hover .mask {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}

.footer .main-info .detail {
  flex: 1;
  position: relative;
  overflow: hidden;
}
.footer .main-info .detail .ctrl {
  position: absolute;
  right: 0px;
  top: 4px;
  padding-right: 6px;
  /* background: #eeeeee; */
}
.footer .main-info .detail .ctrl:first-of-type .icon {
  margin-right: 5px;
}
.footer .main-info .detail .ctrl .icon {
  color: var(--text-default-color);
  opacity: 0.5;
}
.footer .main-info .detail .ctrl .icon:hover {
  opacity: 1;
}

.footer .main-info .detail .title {
  text-align: center;
  font-size: 14px;
  color: var(--text-default-color);
  min-width: 0px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin: 3px 60px 0 60px;
}

.footer .main-info .detail .more-info {
  padding: 0 10px;
  display: flex;
  color: var(--text-subtitle-color);
}

.footer .main-info .detail .more-info .singer {
  flex: 1;
  text-align: center;
  font-size: 12px;
  min-width: 0px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.footer .main-info .detail .more-info .singer a {
  cursor: pointer;
}

.footer .main-info .detail .more-info .current {
  width: 50px;
  font-size: 12px;
}

.footer .main-info .detail .more-info .total {
  width: 50px;
  text-align: right;
  font-size: 12px;
}

.footer .main-info .detail .playbar {
  width: 100%;
}
.footer .main-info .detail .playbar .playbar-clickable {
  padding: 8px 10px;
}
.footer .main-info .detail .playbar .barbg {
  height: 3px;
  background: var(--footer-player-bar-background-color);
}

.footer .main-info .detail .playbar .barbg .cur {
  height: 100%;
  background: var(--footer-player-bar-cur-background-color);
  position: relative;
}

.footer .main-info .detail .playbar .barbg .cur .btn {
  background: var(--footer-player-bar-cur-button-color);
  height: 8px;
  width: 2px;
  position: absolute;
  right: -2px;
  top: -5px;
}

.footer .main-info .detail .playbar .playbar-clickable:hover .barbg .cur .btn {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  top: -3px;
}

.footer .menu-modal {
  left: 0;
  right: 0;
  top: 0;
  position: fixed;
  background: rgba(255, 255, 255, 0.2);
}
.footer .menu-modal.slideup {
  bottom: 60px;
}

.footer .menu {
  background: var(--foot-background-color);
  border: solid 1px var(--foot-border-color);
  border-radius: 3px;
  position: fixed;
  height: 370px;
  bottom: -311px;
  left: 300px;
  right: 300px;
  -webkit-app-region: no-drag;
  transition: all 0.3s;
  overflow: hidden;
}
.footer .menu.slideup {
  bottom: 60px;
}

.footer .menu .menu-header {
  height: 30px;
  border-bottom: solid 1px var(--footer-header-background-color);
  display: flex;
  align-items: center;
  color: #9e9e9e;
  font-size: 12px;
}

.footer .menu .menu-header .menu-title {
  flex: 1;
  padding: 20px;
}

.footer .menu .menu-header .add-all {
  border-right: solid 1px #e5e5e5;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  padding-right: 10px;
}

.footer .menu .menu-header .remove-all {
  margin-left: 10px;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.footer .menu .menu-header .close {
  margin-left: 10px;
  flex: 0 0 25px;
  align-items: center;
  cursor: pointer;
}
.footer .menu .menu-header .add-all span,
.footer .menu .menu-header .remove-all span {
  cursor: pointer;
}

.footer .menu .menu-header .add-all .icon,
.footer .menu .menu-header .remove-all .icon {
  margin-right: 7px;
}

.footer .menu .menu-header .close svg {
  margin-right: 3px;
  height: 16px;
  width: 16px;
  cursor: pointer;
}

.footer .menu ul.menu-list {
  overflow-y: scroll;
  height: 340px;
  font-size: 12px;
}

.footer .menu ul.menu-list li {
  display: flex;
  align-items: center;
  height: 30px;
  padding-right: 20px;
  position: relative;
  margin-bottom: -2px;
  border-top: solid 2px var(--songlist-border-color);
  border-bottom: solid 2px var(--songlist-border-color);
}

.footer .menu ul.menu-list li.even {
  background: var(--footer-menu-even-background-color);
}

.footer .menu ul.menu-list li:hover {
  background: var(--footer-menu-hover-background-color);
}

.footer .menu ul.menu-list li.playing {
  color: var(--important-color);
}
.footer .menu ul.menu-list li .song-status-icon {
  flex: 0 0 20px;
  width: 20px;
  height: 30px;
  text-align: center;
  display: flex;
  align-items: center;
}
.footer .menu ul.menu-list li .song-status-icon svg {
  width: 10px;
  height: 10px;
  fill: var(--important-color);
  stroke: var(--important-color);
  flex: 1;
}
.footer .menu ul.menu-list li .song-title {
  flex: 2;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.footer .menu ul.menu-list li .song-title.disabled {
  color: #777777;
}
.footer .menu ul.menu-list li .song-title a {
  cursor: pointer;
}

.footer .menu ul.menu-list li .song-singer {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}

/* div.visited{
  color: green;
} */

.footer .menu ul.menu-list li .tools {
  flex: 0 0 42px;
  width: 42px;
}
.footer .menu ul.menu-list li .tools .icon {
  color: #666666;
  cursor: pointer;
  opacity: 0.5;
}
.footer .menu ul.menu-list li .tools .icon:first-of-type {
  margin-right: 5px;
}
.footer .menu ul.menu-list li .tools .icon:hover {
  opacity: 1;
}

.footer .menu ul.menu-list li .song-time {
  flex: 1;
  text-align: right;
}

.footer .right-control {
  flex: 0 0 300px;
  background: var(--foot-background-color);
  display: flex;
  align-items: center;
}

.footer .right-control .playlist-toggle {
  margin-left: 29px;
  cursor: pointer;
}

.footer .right-control .playlist-toggle .icon {
  color: var(--player-right-icon-color);
}

.footer .right-control .playlist-toggle .icon:hover {
  color: var(--player-right-icon-hover-color);
}

.footer .right-control .lyric-toggle {
  margin-right: 30px;
  cursor: pointer;
}

.footer .right-control .lyric-toggle .lyric-icon,
.footer .right-control .lyric-toggle .lyric-icon.selected:hover {
  border: solid 1px #7f7f7f;
  height: 16px;
  line-height: 16px;
  font-size: 14px;
  color: #7f7f7f;
  background-color: var(--lyric-icon-background-color);
  user-select: none;
}

.footer .right-control .lyric-toggle .lyric-icon.selected {
  border: solid 1px #7f7f7f;
  background-color: #7f7f7f;
  color: #fff;
}

.footer .right-control .volume-ctrl {
  flex: 1;
  display: flex;
}

.footer .right-control .volume-ctrl .icon {
  flex: 0 0 24px;
  color: var(--volume-icon-color);
  cursor: pointer;
  margin-left: 21px;
}
.footer .right-control .volume-ctrl .m-pbar {
  flex: 1;
}

.footer .right-control .volume-ctrl .barbg {
  height: 3px;

  background: var(--volume-bar-background-color);
  margin-top: 7px;
  width: 140px;
}

.footer .right-control .volume-ctrl .barbg .cur {
  height: 100%;
  background: var(--volume-bar-current-background-color);

  position: relative;
}

.footer .right-control .volume-ctrl .barbg .cur .btn {
  background: #ffffff;
  height: 13px;
  width: 13px;
  border: solid 1px #e4e4e4;
  border-radius: 13px;
  position: absolute;
  right: -13px;
  top: -6px;
}

/* footer end */

/* dialog start */
.shadow {
  position: fixed;
  background: rgba(30, 30, 30, 0.9);
  _position: absolute;
  z-index: 9999;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-image: url(data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==);
}

.dialog {
  position: absolute;
  top: 120px;
  width: 400px;
  height: 430px;
  z-index: 10000;
  overflow: hidden;
  border-radius: 4px;
  background-color: var(--dialog-background-color);
  color: var(--dialog-text-color);
  box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  height: 30px;
  font-size: 15px;
  font-weight: bold;
  text-align: left;
  padding: 12px;
}

.dialog-header .dialog-close {
  float: right;
  font-size: 26px;
  cursor: pointer;
  margin-top: -10px;
}

.dialog-body {
  padding: 0 20px;
  height: 370px;
  overflow-y: auto;
  background-color: var(--dialog-background-color);
}

.dialog-body .buttons {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-body .buttons .confirm-button {
  margin-right: 20px;
}
.dialog .detail-songlist li:hover {
  background-color: #e3e3e5;
  cursor: pointer;
}
.dialog-body input {
  width: 100%;
}

.dialog-playlist,
.dialog-backuplist,
.dialog-merge-playlist {
  padding-left: 0px;
  text-align: left;
}

.dialog-playlist li,
.dialog-backuplist li,
.dialog-merge-playlist li {
  cursor: pointer;
  height: 48px;
  padding: 6px;
}
.dialog-backuplist li {
  height: 96px;
  padding: 6px;
}

.dialog-playlist li:hover,
.dialog-backuplist li:hover,
.dialog-merge-playlist li:hover {
  background-color: var(--dialog-highlight-color);
}

.dialog-playlist li img,
.dialog-backuplist li img,
.dialog-merge-playlist li img {
  float: left;
  height: 48px;
  width: 48px;
}

.dialog-playlist li h2,
.dialog-backuplist li h2,
.dialog-merge-playlist li h2 {
  margin: 0 0 0 58px;
  font-size: 13px;
  font-weight: inherit;
}
.dialog-backuplist li h2 {
  margin-top: 0;
}
.dialog-newplaylist {
  padding: 10px;
}

.dialog-newbackup {
  text-align: center;
}

.dialog-editplaylist label,
.dialog-open-url label {
  display: block;
  height: 30px;
  line-height: 30px;
}

.dialog-editplaylist .dialog-footer {
  position: absolute;
  bottom: 20px;
}
.dialog-body .field-name {
  margin: 10px 0 5px 0;
}

/* dialog end */

/* widget source-list start */
.source-list {
  margin: 20px 26px 10px 26px;
}

.source-list .source-button {
  display: inline-block;
  color: var(--link-inactive-color);
  cursor: pointer;
  padding-bottom: 4px;
  font-size: 14px;
}

.source-list .source-button.active,
.source-list .source-button:hover {
  color: var(--link-active-color);
  border-bottom: solid 1px var(--link-active-color);
}

.source-list .splitter {
  display: inline-block;
  background: #a9a9a9;
  margin-top: 1px;
  height: 12px;
  width: 1px;
  margin: 0 10px;
}
.source-list .search-type {
  float: right;
}
/* widget source-list end */

/* widget playlist-filter start */

.playlist-filter {
  line-height: 38px;
  margin: 0 26px 10px 26px;
}

.playlist-filter .filter-item {
  line-height: 20px;
  padding: 5px 15px;
  margin-right: 10px;
}

.playlist-filter .filter-item.active {
  font-weight: 600;
  background: var(--button-hover-background-color);
}

/* widget playlist-filter end */

/* widget all-playlist-filter start */
.all-playlist-filter .category {
  margin-bottom: 10px;
  display: flex;
}

.all-playlist-filter .category .category-title {
  margin-left: 30px;
  margin-top: 12px;
  min-width: 50px;
  font-size: 18px;
}
.all-playlist-filter .category .category-filters {
  margin-left: 10px;
  display: flex;
  flex-wrap: wrap;
}
.all-playlist-filter .category .category-filters .filter-item {
  min-width: 80px;
  margin-top: 10px;
  display: flex;
}
.all-playlist-filter .category .category-filters .filter-item span {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  padding: 5px 10px;
}
.all-playlist-filter .category .category-filters .filter-item span:hover {
  background-color: var(--button-background-color);
  border-radius: var(--default-border-radius);
}
/* widget all-playlist-filter end */
