[{"id": 1, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://y.qq.com/"}, {"header": "origin", "operation": "set", "value": "https://y.qq.com/"}]}, "condition": {"urlFilter": "|*.y.qq.com", "resourceTypes": ["main_frame", "xmlhttprequest"]}}, {"id": 2, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "user-agent", "operation": "set", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30"}]}, "condition": {"urlFilter": "|*.kugou.com", "resourceTypes": ["main_frame", "xmlhttprequest"]}}, {"id": 3, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://www.bilibili.com/"}]}, "condition": {"urlFilter": "|*.bilivideo.com/", "resourceTypes": ["media"]}}]