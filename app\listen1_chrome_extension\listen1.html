<!DOCTYPE html>
<html lang="en" ng-app="listenone">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags-->
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title ng-bind="document_title">Listen 1</title>

    <link href="css/notyf.min.css" rel="stylesheet" />
    <link href="css/notyf_custom.css" rel="stylesheet" />

    <link href="css/hotkeys.css" rel="stylesheet" />
    <link href="css/icon.css" rel="stylesheet" />

    <link href="css/origin.css" rel="stylesheet" id="theme-css" />
    <link href="css/common.css" rel="stylesheet" id="common-css"/>

    <link href="images/logo_16.png" rel="shortcut icon" />
    <link href="images/logo_16.png" rel="bookmark" />
    <script>
      if (typeof module === 'object') {
        window.module = module;
        module = undefined;
      }
    </script>
    
    <script type="text/javascript" src="js/vendor/angular.min.js"></script>
    <script src="js/vendor/i18next.min.js"></script>
    <script src="js/vendor/i18nextHttpBackend.min.js"></script>
    <script
      type="text/javascript"
      src="js/vendor/forge_listen1_fork.min.js"
    ></script>
    <script type="text/javascript" src="js/vendor/axios.min.js"></script>
    <script type="text/javascript" src="js/vendor/notyf.min.js"></script>
    <script type="text/javascript" src="js/vendor/howler.core.min.js"></script>
    <script type="text/javascript" src="js/vendor/hotkeys.min.js"></script>
    <script type="text/javascript" src="js/vendor/async.min.js"></script>
    <script type="text/javascript" src="js/vendor/lru-cache.min.js"></script>

    <script type="text/javascript" src="js/lowebutil.js"></script>
    <script type="text/javascript" src="js/github.js"></script>
    <script type="text/javascript" src="js/lastfm.js"></script>
    <script type="text/javascript" src="js/provider/xiami.js"></script>
    <script type="text/javascript" src="js/provider/qq.js"></script>
    <script type="text/javascript" src="js/provider/netease.js"></script>
    <script type="text/javascript" src="js/provider/kugou.js"></script>
    <script type="text/javascript" src="js/provider/kuwo.js"></script>
    <script type="text/javascript" src="js/provider/bilibili.js"></script>
    <script type="text/javascript" src="js/provider/migu.js"></script>
    <script type="text/javascript" src="js/provider/taihe.js"></script>
    <script type="text/javascript" src="js/provider/localmusic.js"></script>

    <script type="text/javascript" src="js/bridge.js"></script>
    <script type="text/javascript" src="js/player_thread.js"></script>
    <script type="text/javascript" src="js/myplaylist.js"></script>
    <script type="text/javascript" src="js/loweb.js"></script>
    <script type="text/javascript" src="js/l1_player.js"></script>
    <script type="text/javascript" src="js/app.js"></script>
    <script type="text/javascript" src="js/controller/profile.js"></script>
    <script type="text/javascript" src="js/controller/auth.js"></script>
    <script type="text/javascript" src="js/controller/navigation.js"></script>
    <script type="text/javascript" src="js/controller/my_playlist.js"></script>
    <script type="text/javascript" src="js/controller/platform.js"></script>
    <script type="text/javascript" src="js/controller/playlist.js"></script>
    <script type="text/javascript" src="js/controller/play.js"></script>
    <script
      type="text/javascript"
      src="js/controller/instant_search.js"
    ></script>
    <script>
      if (window.module) module = window.module;
    </script>

  </head>

  <body>
    <div ng-controller="ProfileController" ng-init="initProfile()">
      <div class="body" ng-if="theme==='white' || theme==='black' ">
        <div
        id="feather-container"
        style="visibility: hidden; position: absolute; width: 0px; height: 0px"
      ></div>
        <div
          ng-controller="PlayController as playCtrl"
          ng-init="loadLocalSettings()"
        >
          <div ng-controller="AuthController" ng-init="refreshAuthStatus()">
            <div>
              <div ng-controller="NavigationController">
                <div class="wrap">
                  <!-- dialog-->
                  <div class="shadow" ng-hide="is_dialog_hidden==1"></div>
                  <div
                    class="dialog"
                    ng-hide="is_dialog_hidden==1"
                    ng-style="myStyle"
                  >
                    <div class="dialog-header">
                      <span>{{ dialog_title }}</span
                      ><span class="dialog-close" ng-click="closeDialog()">×</span>
                    </div>
                    <div class="dialog-body">
                      <!-- choose playlist dialog-->
                      <ul class="dialog-playlist" ng-show="dialog_type==0">
                        <li class="detail-add" ng-click="newDialogOption(1)">
                          <img src="images/mycover.jpg" />
                          <h2>{{_CREATE_PLAYLIST}}</h2>
                        </li>
                        <li
                          ng-repeat="playlist in myplaylist track by $index"
                          ng-class-odd="'odd'"
                          ng-class-even="'even'"
                          ng-click="chooseDialogOption(playlist.info.id)"
                        >
                          <img ng-src="{{ playlist.info.cover_img_url }}" />
                          <h2>{{ playlist.info.title }}</h2>
                        </li>
                      </ul>
                      <!-- create new playlist dialog-->
                      <div class="dialog-newplaylist" ng-show="dialog_type==1">
                        <input
                          class="form-control"
                          type="text"
                          placeholder="{{_INPUT_NEW_PLAYLIST_TITLE}}"
                          ng-model="newlist_title"
                        />
                        <div class="buttons">
                          <button
                            class="btn btn-primary confirm-button"
                            ng-click="createAndAddPlaylist()"
                          >
                            {{_CONFIRM}}
                          </button>
                          <button
                            class="btn btn-default"
                            ng-click="cancelNewDialog(0)"
                          >
                            {{_CANCEL}}
                          </button>
                        </div>
                      </div>
                      <!-- edit playlist dialog-->
                      <div class="dialog-editplaylist" ng-show="dialog_type==3">
                        <div class="form-group">
                          <label>{{_PLAYLIST_TITLE}}</label>
                          <input
                            class="form-control"
                            type="text"
                            placeholder="{{_INPUT_PLAYLIST_TITLE}}"
                            ng-model="dialog_playlist_title"
                          />
                        </div>
                        <div class="form-group">
                          <label>{{_PLAYLIST_COVER_IMAGE_URL}}</label>
                          <input
                            class="form-control"
                            type="text"
                            placeholder="{{_INPUT_PLAYLIST_COVER_IMAGE_URL}}"
                            ng-model="dialog_cover_img_url"
                          />
                        </div>
                        <div class="buttons">
                          <button
                            class="btn btn-primary confirm-button"
                            ng-click="editMyPlaylist(list_id)"
                          >
                            {{_CONFIRM}}
                          </button>
                          <button class="btn btn-default" ng-click="closeDialog()">
                            {{_CANCEL}}
                          </button>
                        </div>
                        <div class="dialog-footer">
                          <button
                            class="btn btn-danger remove-button"
                            ng-click="removeMyPlaylist(list_id)"
                          >
                            {{_REMOVE_PLAYLIST}}
                          </button>
                        </div>
                      </div>
                      <div class="dialog-connect-lastfm" ng-show="dialog_type==4">
                        <p>{{_OPENING_LASTFM_PAGE}}</p>
                        <p>{{_CONFIRM_NOTICE_LASTFM}}</p>
                        <div class="buttons">
                          <button
                            class="btn btn-primary confirm-button"
                            ng-click="lastfm.updateStatus();closeDialog();"
                          >
                            {{_AUTHORIZED_FINISHED}}
                          </button>
                          <button
                            class="btn btn-warning warning-button"
                            ng-click="lastfm.getAuth();"
                          >
                            {{_AUTHORIZED_REOPEN}}
                          </button>
                        </div>
                      </div>
                      <!-- open playlist dialog-->
                      <div class="dialog-open-url" ng-show="dialog_type==5">
                        <div class="form-group">
                          <label>{{_PLAYLIST_LINK}}</label>
                          <input
                            class="form-control"
                            type="text"
                            placeholder="{{_EXAMPLE}}https://www.xiami.com/collect/198267231"
                            ng-model="dialog_url"
                          />
                        </div>
                        <div class="buttons">
                          <button
                            class="btn btn-primary confirm-button"
                            ng-click="openUrl(dialog_url);closeDialog();dialog_url='';"
                          >
                            {{_CONFIRM}}
                          </button>
                          <button class="btn btn-default" ng-click="closeDialog()">
                            {{_CANCEL}}
                          </button>
                        </div>
                      </div>
                      <ul class="dialog-merge-playlist" ng-show="dialog_type==6">
                        <li
                          ng-repeat="playlist in myplaylist track by $index"
                          ng-class-odd="'odd'"
                          ng-class-even="'even'"
                          ng-click="mergePlaylist(playlist.info.id)"
                        >
                          <img ng-src="{{ playlist.info.cover_img_url }}" />
                          <h2>{{ playlist.info.title }}</h2>
                        </li>
                      </ul>
                      <div class="dialog-connect-github" ng-show="dialog_type==7">
                        <p>{{_OPENING_GITHUB_PAGE}}</p>
                        <p>{{_CONFIRM_NOTICE_GITHUB}}</p>
                        <div class="buttons">
                          <button
                            class="btn btn-primary confirm-button"
                            ng-click="updateGithubStatus();closeDialog();"
                          >
                            {{_AUTHORIZED_FINISHED}}
                          </button>
                          <button
                            class="btn btn-warning warning-button"
                            ng-click="openGithubAuth();"
                          >
                            {{_AUTHORIZED_REOPEN}}
                          </button>
                        </div>
                      </div>
                      <ul class="dialog-backuplist" ng-show="dialog_type==8">
                        <li class="detail-add" ng-click="newDialogOption(9)">
                          <img src="images/mycover.jpg" />
                          <h2>{{_CREATE_PLAYLIST_BACKUP}}</h2>
                        </li>
                        <li
                          ng-repeat="backup in myBackup track by $index"
                          ng-class-odd="'odd'"
                          ng-class-even="'even'"
                          ng-click="backupMySettings2Gist(backup.id, backup.public); closeDialog();"
                        >
                          <img ng-src="images/mycover.jpg" />
                          <h2>
                            {{ backup.id }}<br />
                            {{backup.description}}
                          </h2>
                        </li>
                      </ul>
                      <!-- create new backup dialog-->
                      <div class="dialog-newbackup" ng-show="dialog_type==9">
                        <button
                          class="btn btn-primary confirm-button"
                          ng-click="backupMySettings2Gist(null, true);closeDialog();"
                        >
                          {{_CREATE_PUBLIC_BACKUP}}
                        </button>
                        <button
                          class="btn btn-primary confirm-button"
                          ng-click="backupMySettings2Gist(null, false);closeDialog();"
                        >
                          {{_CREATE_PRIVATE_BACKUP}}
                        </button>
                        <button
                          class="btn btn-default"
                          ng-click="cancelNewDialog(8)"
                        >
                          {{_CANCEL}}
                        </button>
                      </div>
                      <ul class="dialog-backuplist" ng-show="dialog_type==10">
                        <li
                          ng-repeat="backup in myBackup track by $index"
                          ng-class-odd="'odd'"
                          ng-class-even="'even'"
                          ng-click="importMySettingsFromGist(backup.id); closeDialog();"
                        >
                          <img ng-src="images/mycover.jpg" />
                          <h2>{{ backup.id }} {{backup.description}}</h2>
                        </li>
                      </ul>
                      <div class="dialog-open-login" ng-show="dialog_type==11">
                        <p>{{_LOGIN_DIALOG_NOTICE}}</p>
                        <div class="buttons">
                          <button
                            class="btn btn-primary confirm-button"
                            ng-click="closeDialog();refreshAuthStatus();"
                          >
                            {{_LOGIN_SUCCESS}}
                          </button>
                          <button
                            class="btn btn-warning warning-button"
                            ng-click="openLogin(dialog_data);"
                          >
                            {{_LOGIN_FAIL_RETRY}}
                          </button>
                        </div>
                      </div>
                      <div class="dialog-proxy" ng-show="dialog_type==12">
                        <select
                          ng-options="mode.displayText for mode in proxyModes"
                          ng-model="proxyModeInput"
                          ng-change="changeProxyMode(proxyModeInput)"
                        ></select>
                        <div
                          ng-show="proxyModeInput.name=='custom'"
                          class="custom-proxy"
                        >
                          <div class="rule-input">
                            <div class="field-name">{{_PROTOCOL}}</div>
                            <select
                              ng-options="protocol for protocol in proxyProtocols"
                              ng-model="proxyProtocol"
                              ng-change="changeProxyProtocol(proxyProtocol)"
                            ></select>
                            <div class="field-name">{{_HOST}}</div>
                            <input type="text" id="proxy-rules-host" />
                            <div class="field-name">{{_PORT}}</div>
                            <input type="text" id="proxy-rules-port" />
                          </div>
                        </div>
                        <div class="buttons">
                          <button
                            class="btn btn-primary confirm-button"
                            ng-click="setProxyConfig();closeDialog();"
                          >
                            {{_CONFIRM}}
                          </button>
                          <button
                            class="btn btn-warning warning-button"
                            ng-click="closeDialog();"
                          >
                            {{_CANCEL}}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
    
                  <div class="main" ng-controller="MyPlayListController">
                    <div class="sidebar">
                      <div class="flex-scroll-wrapper">
                        <div class="menu-control"></div>
                        <div class="menu-title">
                          <div class="title">{{_PLATFORM_UNION}}</div>
                        </div>
                        <ul class="nav masthead-nav">
                          <li
                            ng-class="{ 'active':(current_tag==2) && (window_url_stack.length ==0) }"
                            ng-click="showTag(2)"
                          >
                            <div class="sidebar-block">
                              <span class="icon li-featured-list"></span
                              ><a>{{_PLAYLISTS}}</a>
                            </div>
                          </li>
                        </ul>
                        <div
                          ng-if="!isChrome || is_login('netease') || is_login('qq')"
                          class="menu-title"
                        >
                          <div class="title">{{_MY_MUSIC}}</div>
                        </div>
                        <ul class="nav masthead-nav">
                          <li
                            ng-if="!isChrome"
                            ng-click="showPlaylist('lmplaylist_reserve')"
                            ng-class="{ 'active':window_type=='list' && ( '/playlist?list_id=lmplaylist_reserve' === getCurrentUrl() ) }"
                          >
                            <div class="sidebar-block">
                              <span class="icon li-featured-list"></span
                              ><a>{{_LOCAL_MUSIC}}</a>
                            </div>
                          </li>
                          <li
                            ng-if="is_login('netease')"
                            ng-click="showTag(6, {platform:'netease', user: musicAuth.netease});"
                            ng-class="{ 'active':(current_tag==6 && tag_params.platform=='netease') && (window_url_stack.length ==0) }"
                          >
                            <div class="sidebar-block">
                              <svg class="feather">
                                <use href="#globe"></use>
                              </svg>
                              <a>{{_MY_NETEASE}}</a>
                            </div>
                          </li>
                          <li
                            ng-if="is_login('qq')"
                            ng-click="showTag(6, {platform:'qq', user: musicAuth.qq});"
                            ng-class="{ 'active':(current_tag==6 && tag_params.platform=='qq') && (window_url_stack.length ==0) }"
                          >
                            <div class="sidebar-block">
                              <svg class="feather">
                                <use href="#globe"></use>
                              </svg>
                              <a>{{_MY_QQ}}</a>
                            </div>
                          </li>
                        </ul>
                        <div class="menu-title" ng-init="loadMyPlaylist();">
                          <div class="title">{{_CREATED_PLAYLIST}}</div>
                          <svg class="feather icon" ng-click="showDialog(5)">
                            <use href="#plus-square"></use>
                          </svg>
                        </div>
                        <ul class="nav masthead-nav">
                          <li
                            ng-repeat="i in myplaylists track by $index"
                            ng-class="{ 'active':window_type=='list' && ( ('/playlist?list_id='+i.info.id) === getCurrentUrl() ) }"
                            ng-click="showPlaylist(i.info.id)"
                            drag-drop-zone
                            drag-zone-type="'application/listen1-myplaylist'"
                            drop-zone-ondrop="onSidebarPlaylistDrop('my', i.info.id, arg1, arg2, arg3)"
                            draggable="true"
                            sortable="true"
                            drag-zone-object="i"
                            drag-zone-title="i.info.title"
                          >
                            <div class="sidebar-block">
                              <svg class="feather">
                                <use href="#disc"></use>
                              </svg>
                              <a>{{i.info.title}}</a>
                            </div>
                          </li>
                        </ul>
                        <div class="menu-title" ng-init="loadFavoritePlaylist();">
                          <div class="title">{{_FAVORITED_PLAYLIST}}</div>
                        </div>
                        <ul class="nav masthead-nav">
                          <li
                            ng-repeat="i in favoriteplaylists track by $index"
                            ng-class="{ 'active':window_type=='list' && ( ('/playlist?list_id='+i.info.id) === getCurrentUrl() ) }"
                            ng-click="showPlaylist(i.info.id, {useCache: false})"
                            drag-drop-zone
                            drag-zone-type="'application/listen1-favoriteplaylist'"
                            drop-zone-ondrop="onSidebarPlaylistDrop('favorite', i.info.id, arg1, arg2, arg3)"
                            draggable="true"
                            sortable="true"
                            drag-zone-object="i"
                            drag-zone-title="i.info.title"
                          >
                            <div class="sidebar-block">
                              <svg class="feather">
                                <use href="#disc"></use>
                              </svg>
                              <a>{{i.info.title}}</a>
                            </div>
                          </li>
                        </ul>
                      </div>
                    </div>
    
                    <div class="content" ng-controller="InstantSearchController">
                      <div class="navigation">
                        <div class="backfront">
                          <span class="icon li-back" ng-click="popWindow()"></span>
                          <span
                            class="icon li-advance"
                            ng-click="forwardWindow()"
                          ></span>
                        </div>
                        <div class="search">
                          <input
                            class="form-control search-input"
                            id="search-input"
                            type="text"
                            ng-model="keywords"
                            placeholder="{{_SEARCH_PLACEHOLDER}}"
                            ng-model-options="{debounce: 500}"
                            ng-keyup="enterEvent($event)"
                          />
                        </div>
                        <div
                          ng-class="{ 'active': (current_tag==4) && (window_url_stack.length ==0)}"
                          ng-click="showTag(5)"
                          class="settings"
                        >
                          <span class="icon">
                            <svg class="feather">
                              <use href="#users"></use>
                            </svg>
                          </span>
                        </div>
                        <div
                          ng-class="{ 'active': (current_tag==4) && (window_url_stack.length ==0)}"
                          ng-click="showTag(4)"
                          class="settings"
                        >
                          <span class="icon li-setting"></span>
                        </div>
                        <div ng-if="!isChrome && !isMac" class="window-control">
                          <svg class="icon" window-control="window_min">
                            <use href="#minimize-2"></use>
                          </svg>
                          <svg class="icon" window-control="window_max">
                            <use href="#maximize"></use>
                          </svg>
                          <svg class="icon" window-control="window_close">
                            <use href="#x"></use>
                          </svg>
                        </div>
                      </div>
                      <div
                        class="browser flex-scroll-wrapper"
                        infinite-scroll="scrolling()"
                        content-selector="'#playlist-content'"
                      >
                        <!-- hot playlist window-->
                        <div
                          class="page page-hot-playlist"
                          ng-show="current_tag==2 && is_window_hidden==1"
                          ng-controller="PlayListController"
                          ng-init="loadPlaylist();"
                        >
                          <div class="source-list" ng-show="is_window_hidden==1">
                            <div
                              ng-repeat-start="source in ::sourceList"
                              class="source-button"
                              ng-class="{'active':tab === source.name}"
                              ng-click="changeTab(source.name)"
                            >
                              {{source.displayText}}
                            </div>
                            <div
                              ng-repeat-end
                              ng-if="!$last"
                              class="splitter"
                            ></div>
                          </div>
                          <div class="playlist-filter">
                            <div
                              class="l1-button filter-item"
                              ng-repeat="filter in playlistFilters[tab] || []"
                              ng-click="changeFilter(filter.id)"
                              ng-class="{'active':filter.id === currentFilterId}"
                            >
                              {{filter.name}}
                            </div>
                            <div
                              class="l1-button filter-item"
                              ng-show="playlistFilters[tab] && playlistFilters[tab].length > 0"
                              ng-click="toggleMorePlaylists()"
                            >
                              更多...
                            </div>
                          </div>
                          <div class="all-playlist-filter" ng-show="showMore">
                            <div
                              ng-repeat="category in allPlaylistFilters[tab] || []"
                              class="category"
                            >
                              <div class="category-title">
                                {{category.category}}
                              </div>
                              <div class="category-filters">
                                <div
                                  class="filter-item"
                                  ng-repeat="filter in category.filters"
                                >
                                  <span ng-click="changeFilter(filter.id)">
                                    {{filter.name}}</span
                                  >
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="site-wrapper-innerd" id="hotplaylist">
                            <div class="cover-container" id="playlist-content">
                              <ul class="playlist-covers">
                                <li ng-repeat="i in result ">
                                  <div class="u-cover">
                                    <img
                                      ng-src="{{i.cover_img_url}}"
                                      ng-click="showPlaylist(i.id)"
                                    />
                                    <div
                                      class="bottom"
                                      ng-click="directplaylist(i.id)"
                                    >
                                      <svg class="feather">
                                        <use href="#play-circle"></use>
                                      </svg>
                                    </div>
                                  </div>
                                  <div class="desc">
                                    <span
                                      class="title"
                                      ng-click="showPlaylist(i.id)"
                                      >{{i.title}}</span
                                    >
                                  </div>
                                </li>
                                <!-- <div class="loading_bottom">
                                  <img src="images/loading-1.gif" height="40px" />
                                </div> -->
                              </ul>
                            </div>
                          </div>
                        </div>
                        <!-- my platform window-->
                        <div
                          class="page page-hot-playlist"
                          ng-show="current_tag==6 && is_window_hidden==1"
                          ng-controller="PlatformController"
                        >
                          <div class="source-list" ng-show="is_window_hidden==1">
                            <div
                              ng-repeat-start="source in ::platformSourceList"
                              class="source-button"
                              ng-class="{'active':tab === source.name}"
                              ng-click="changeTab(source.name)"
                            >
                              {{source.displayText}}
                            </div>
                            <div
                              ng-repeat-end
                              ng-if="!$last"
                              class="splitter"
                            ></div>
                          </div>
                          <div class="site-wrapper-innerd" id="hotplaylist">
                            <div class="cover-container" id="playlist-content">
                              <ul class="playlist-covers">
                                <li ng-repeat="i in myPlatformPlaylists">
                                  <div class="u-cover">
                                    <img
                                      ng-src="{{i.cover_img_url}}"
                                      ng-click="showPlaylist(i.id)"
                                    />
                                    <div
                                      class="bottom"
                                      ng-click="directplaylist(i.id)"
                                    >
                                      <svg class="feather">
                                        <use href="#play-circle"></use>
                                      </svg>
                                    </div>
                                  </div>
                                  <div class="desc">
                                    <span
                                      class="title"
                                      ng-click="showPlaylist(i.id)"
                                      >{{i.title}}</span
                                    >
                                  </div>
                                </li>
                              </ul>
                            </div>
                          </div>
                        </div>
    
                        <!-- content page: 快速搜索 -->
                        <div
                          class="page"
                          ng-show="current_tag==3 && is_window_hidden==1"
                        >
                          <div class="site-wrapper-innerd">
                            <div class="cover-container">
                              <!-- Initialize a new AngularJS app and associate it with a module named "instantSearch"-->
                              <div class="searchbox">
                                <ul class="source-list">
                                  <li
                                    class="source-button"
                                    ng-class="{'active':tab === 'allmusic'}"
                                    ng-click="changeSourceTab('allmusic')"
                                  >
                                    <a>{{_ALL_MUSIC}}(Beta)</a>
                                  </li>
                                  <div class="splitter"></div>
                                  <div
                                    ng-repeat-start="source in ::sourceList"
                                    class="source-button"
                                    ng-class="{'active':tab === source.name}"
                                    ng-click="changeSourceTab(source.name)"
                                  >
                                    {{source.displayText}}
                                  </div>
                                  <div
                                    ng-repeat-end
                                    ng-if="!$last"
                                    class="splitter"
                                  ></div>
                                  <svg
                                    class="searchspinner"
                                    ng-show="loading"
                                    version="1.1"
                                    id="loader-1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    x="0px"
                                    y="0px"
                                    width="40px"
                                    height="40px"
                                    viewBox="0 0 40 40"
                                    enable-background="new 0 0 40 40"
                                    xml:space="preserve"
                                  >
                                    <path
                                      opacity="0.2"
                                      fill="#000"
                                      d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946 s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634 c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"
                                    />
                                    <path
                                      fill="#000"
                                      d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0 C22.32,8.481,24.301,9.057,26.013,10.047z"
                                    >
                                      <animateTransform
                                        attributeType="xml"
                                        attributeName="transform"
                                        type="rotate"
                                        from="0 20 20"
                                        to="360 20 20"
                                        dur="0.6s"
                                        repeatCount="indefinite"
                                      />
                                    </path>
                                  </svg>
                                  <div class="search-type">
                                    <li
                                      class="source-button"
                                      ng-class="{'active':isSearchType(0)}"
                                      ng-click="changeSearchType(0)"
                                    >
                                      <a>单曲</a>
                                    </li>
                                    <div class="splitter"></div>
                                    <li
                                      class="source-button"
                                      ng-class="{'active':isSearchType(1)}"
                                      ng-click="changeSearchType(1)"
                                    >
                                      <a>歌单</a>
                                    </li>
                                  </div>
                                </ul>
                                <ul class="detail-songlist">
                                  <li class="head" ng-if="searchType===0 ">
                                    <div class="title"><a>{{_SONGS}}</a></div>
                                    <div class="artist"><a>{{_ARTISTS}}</a></div>
                                    <div class="album"><a>{{_ALBUMS}}</a></div>
                                    <div class="tools">{{_OPERATION}}</div>
                                  </li>
                                  <li class="head" ng-if="searchType===1 ">
                                    <div class="title">
                                      <a>{{_PLAYLIST_TITLE}}</a>
                                    </div>
                                    <div class="artist">
                                      <a>{{_PLAYLIST_AUTHOR}}</a>
                                    </div>
                                    <div class="album">
                                      <a>{{_PLAYLIST_SONG_COUNT}}</a>
                                    </div>
                                  </li>
                                  <li
                                    ng-if="searchType===0"
                                    ng-repeat="song in result"
                                    ng-class-odd="'odd'"
                                    ng-class-even="'even'"
                                    ng-mouseenter="options=true"
                                    ng-mouseleave="options=false"
                                    ng-dblclick="addAndPlay(song)"
                                  >
                                    <div class="title">
                                      <!-- <a ng-if="song.disabled" class="disabled" ng-click="copyrightNotice()">{{ song.title |limitTo:30}}</a> -->
                                      <a add-and-play="song"
                                        ><span
                                          ng-if="isActiveTab('allmusic')"
                                          class="source"
                                          >{{song.sourceName}}</span
                                        >{{ song.title |limitTo:30}}</a
                                      >
                                    </div>
                                    <div class="artist">
                                      <a ng-click="showPlaylist(song.artist_id)"
                                        >{{ song.artist |limitTo:20}}</a
                                      >
                                    </div>
                                    <div class="album">
                                      <a ng-click="showPlaylist(song.album_id)"
                                        >{{ song.album |limitTo:30}}</a
                                      >
                                    </div>
    
                                    <div class="tools">
                                      <a
                                        title="{{_ADD_TO_QUEUE}}"
                                        class="detail-add-button"
                                        add-without-play="song"
                                        ng-show="options"
                                        ><span class="icon li-add"></span
                                      ></a>
                                      <a
                                        title="{{_ADD_TO_PLAYLIST}}"
                                        class="detail-fav-button"
                                        ng-show="options"
                                        ng-click="showDialog(0, song)"
                                        ><span class="icon li-songlist"></span
                                      ></a>
                                      <a
                                        title="{{_REMOVE_FROM_PLAYLIST}}"
                                        class="detail-delete-button"
                                        ng-click="removeSongFromPlaylist(song, list_id)"
                                        ng-show="options && is_mine=='1' "
                                        ><span class="icon li-del"></span
                                      ></a>
                                      <a
                                        title="{{_ORIGIN_LINK}}"
                                        class="source-button"
                                        open-url="song.source_url"
                                        ng-show="options"
                                        ><span class="icon li-link"></span
                                      ></a>
                                    </div>
                                  </li>
                                  <li
                                    ng-if="searchType===1"
                                    ng-repeat="playlist in result"
                                    ng-class-odd="'odd'"
                                    ng-class-even="'even'"
                                    class="playlist-result"
                                  >
                                    <div class="title">
                                      <a ng-click="showPlaylist(playlist.id)">
                                        <img
                                          ng-src="{{ playlist.img_url }}"
                                          err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png"
                                        />
                                        <div>
                                          {{ playlist.title |limitTo:30}}<span
                                            ng-if="isActiveTab('allmusic')"
                                            class="source playlist"
                                            >{{playlist.sourceName}}</span
                                          >
                                        </div>
                                      </a>
                                    </div>
                                    <div class="artist">
                                      {{ playlist.author |limitTo:20}}
                                    </div>
                                    <div class="album">
                                      {{ playlist.count |limitTo:30}}
                                    </div>
                                  </li>
                                </ul>
                                <div
                                  class="search-pagination"
                                  ng-show="totalpage>1"
                                  pagination
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
    
                        <!-- content page: 设置 -->
                        <div
                          class="page"
                          ng-show="current_tag==4 && is_window_hidden==1"
                          ng-init="lastfm.updateStatus(); updateGithubStatus();"
                        >
                          <div class="site-wrapper-innerd">
                            <div class="cover-container">
                              <div class="settings-title">
                                <span>{{_LANGUAGE}}</span>
                              </div>
                              <div class="settings-content">
                                <div>
                                  <button
                                    class="language-button"
                                    ng-click="setLang('zh-CN')"
                                  >
                                    简体中文
                                  </button>
                                  <button
                                    class="language-button"
                                    ng-click="setLang('zh-TC')"
                                  >
                                    繁体中文
                                  </button>
                                  <button
                                    class="language-button"
                                    ng-click="setLang('en-US')"
                                  >
                                    English
                                  </button>
                                  <button
                                    class="language-button"
                                    ng-click="setLang('fr-FR')"
                                  >
                                    French
                                  </button>
                                  <button
                                    class="language-button"
                                    ng-click="setLang('ko-KR')"
                                  >
                                    Korean
                                  </button>
                                  <button 
                                    class="language-button" 
                                    ng-click="setLang('pt-BR')"
                                  >
                                    Brazilian Portuguese
                                  </button>
                                </div>
                              </div>
                              <div class="settings-title">
                                <span>{{_THEME}}</span>
                              </div>
                              <div class="settings-content">
                                <div>
                                  <button
                                    class="theme-button"
                                    ng-click="setTheme('white')"
                                  >
                                    {{_THEME_WHITE}}
                                  </button>
                                  <button
                                    class="theme-button"
                                    ng-click="setTheme('black')"
                                  >
                                    {{_THEME_BLACK}}
                                  </button>
                                  <button
                                  class="theme-button"
                                  ng-click="setTheme('white2')"
                                >
                                  {{_THEME_MODERN_WHITE}}
                                </button>
                                <button
                                  class="theme-button"
                                  ng-click="setTheme('black2')"
                                >
                                  {{_THEME_MODERN_BLACK}}
                                </button>
                                </div>
                              </div>
                              <div class="settings-title">
                                <span>{{_AUTO_CHOOSE_SOURCE}}</span>
                              </div>
                              <div class="settings-content">
                                <div
                                  class="shortcut"
                                  class="btn btn-primary confirm-button"
                                >
                                  <svg
                                    class="feather"
                                    ng-show="!enableAutoChooseSource"
                                    ng-click="setAutoChooseSource(true)"
                                  >
                                    <use href="#square"></use>
                                  </svg>
                                  <svg
                                    class="feather"
                                    ng-show="enableAutoChooseSource"
                                    ng-click="setAutoChooseSource(true)"
                                  >
                                    <use href="#check-square"></use>
                                  </svg>
                                  {{_AUTO_CHOOSE_SOURCE_NOTICE}}
                                </div>
                                <div
                                  class="search-description"
                                  ng-show="enableAutoChooseSource"
                                >
                                  {{_AUTO_CHOOSE_SOURCE_LIST}}
                                </div>
                                <div
                                  class="search-source-list"
                                  ng-show="enableAutoChooseSource"
                                >
                                  <div
                                    ng-repeat="item in sourceList"
                                    class="search-source"
                                  >
                                    <svg
                                      class="feather"
                                      ng-show="autoChooseSourceList.indexOf(item.name) === -1"
                                      ng-click="enableSource(item.name)"
                                    >
                                      <use href="#square"></use>
                                    </svg>
                                    <svg
                                      class="feather"
                                      ng-show="autoChooseSourceList.indexOf(item.name) > -1"
                                      ng-click="disableSource(item.name)"
                                    >
                                      <use href="#check-square"></use>
                                    </svg>
                                    {{item.displayText}}
                                  </div>
                                </div>
                              </div>
                              <div ng-if="isChrome" class="settings-title">
                                <span
                                  >{{_CLOSE_TAB_ACTION}}({{_VALID_AFTER_RESTART}})</span
                                >
                              </div>
                              <div ng-if="isChrome" class="settings-content">
                                <div class="shortcut">
                                  <svg
                                    class="feather"
                                    ng-show="!enableStopWhenClose"
                                    ng-click="setStopWhenClose(true)"
                                  >
                                    <use href="#square"></use>
                                  </svg>
                                  <svg
                                    class="feather"
                                    ng-show="enableStopWhenClose"
                                    ng-click="setStopWhenClose(false)"
                                  >
                                    <use href="#check-square"></use>
                                  </svg>
                                  <span style="margin-right: 20px"
                                    >{{_QUIT_APPLICATION}}</span
                                  >
                                  <svg
                                    class="feather"
                                    ng-show="enableStopWhenClose"
                                    ng-click="setStopWhenClose(false)"
                                  >
                                    <use href="#square"></use>
                                  </svg>
                                  <svg
                                    class="feather"
                                    ng-show="!enableStopWhenClose"
                                    ng-click="setStopWhenClose(true)"
                                  >
                                    <use href="#check-square"></use>
                                  </svg>
                                  <span> {{_MINIMIZE_TO_BACKGROUND}}</span>
                                </div>
                              </div>
                              <div class="settings-title">
                                <span>{{_NOWPLAYING_DISPLAY}}</span>
                              </div>
                              <div class="settings-content">
                                <div class="shortcut">
                                  <svg
                                    class="feather"
                                    ng-show="!enableNowplayingCoverBackground"
                                    ng-click="setNowplayingCoverBackground(true)"
                                  >
                                    <use href="#square"></use>
                                  </svg>
                                  <svg
                                    class="feather"
                                    ng-show="enableNowplayingCoverBackground"
                                    ng-click="setNowplayingCoverBackground(true)"
                                  >
                                    <use href="#check-square"></use>
                                  </svg>
                                  {{_NOWPLAYING_COVER_BACKGROUND_NOTICE}}
                                </div>
                                <div class="shortcut">
                                  <svg
                                    class="feather"
                                    ng-show="!enableNowplayingBitrate"
                                    ng-click="setNowplayingBitrate(true)"
                                  >
                                    <use href="#square"></use>
                                  </svg>
                                  <svg
                                    class="feather"
                                    ng-show="enableNowplayingBitrate"
                                    ng-click="setNowplayingBitrate(true)"
                                  >
                                    <use href="#check-square"></use>
                                  </svg>
                                  {{_NOWPLAYING_BITRATE_NOTICE}}
                                </div>
                                <div class="shortcut">
                                  <svg
                                    class="feather"
                                    ng-show="!enableNowplayingPlatform"
                                    ng-click="setNowplayingPlatform(true)"
                                  >
                                    <use href="#square"></use>
                                  </svg>
                                  <svg
                                    class="feather"
                                    ng-show="enableNowplayingPlatform"
                                    ng-click="setNowplayingPlatform(true)"
                                  >
                                    <use href="#check-square"></use>
                                  </svg>
                                  {{_NOWPLAYING_PLATFORM_NOTICE}}
                                </div>
                              </div>
                              <div class="settings-title">
                                <span>{{_LYRIC_DISPLAY}}</span>
                              </div>
                              <div class="settings-content">
                                <div class="shortcut" ng-if="!isChrome">
                                  <svg
                                    class="feather"
                                    ng-show="!enableLyricFloatingWindow"
                                    ng-click="openLyricFloatingWindow(true)"
                                  >
                                    <use href="#square"></use>
                                  </svg>
                                  <svg
                                    class="feather"
                                    ng-show="enableLyricFloatingWindow"
                                    ng-click="openLyricFloatingWindow(true)"
                                  >
                                    <use href="#check-square"></use>
                                  </svg>
                                  <span ng-show="enableLyricFloatingWindow"></span
                                  >{{_SHOW_DESKTOP_LYRIC}}
                                </div>
                                <div class="shortcut">
                                  <svg
                                    class="feather"
                                    ng-show="!enableLyricTranslation"
                                    ng-click="toggleLyricTranslation()"
                                  >
                                    <use href="#square"></use>
                                  </svg>
                                  <svg
                                    class="feather"
                                    ng-show="enableLyricTranslation"
                                    ng-click="toggleLyricTranslation()"
                                  >
                                    <use href="#check-square"></use>
                                  </svg>
                                  <span ng-show="enableLyricTranslation"></span
                                  >{{_SHOW_LYRIC_TRANSLATION}}
                                </div>
                                <div class="shortcut" ng-if="!isChrome">
                                  <svg
                                    class="feather"
                                    ng-show="!enableLyricFloatingWindowTranslation"
                                    ng-click="toggleLyricFloatingWindowTranslation()"
                                  >
                                    <use href="#square"></use>
                                  </svg>
                                  <svg
                                    class="feather"
                                    ng-show="enableLyricFloatingWindowTranslation"
                                    ng-click="toggleLyricFloatingWindowTranslation()"
                                  >
                                    <use href="#check-square"></use>
                                  </svg>
                                  <span
                                    ng-show="enableLyricFloatingWindowTranslation"
                                  ></span
                                  >{{_SHOW_DESKTOP_LYRIC_TRANSLATION}}
                                </div>
                              </div>
                              <div class="settings-title">
                                <span>{{_BACKUP_PLAYLIST}}</span>
                              </div>
                              <div class="settings-content">
                                <p>{{_BACKUP_WARNING}}</p>
                                <div>
                                  <button
                                    class="btn btn-primary confirm-button"
                                    ng-click="backupMySettings()"
                                  >
                                    {{_EXPORT_TO_LOCAL_FILE}}
                                  </button>
                                  <button
                                    class="btn btn-primary confirm-button"
                                    ng-show="githubStatus == 2"
                                    ng-click="showDialog(8)"
                                  >
                                    {{_EXPORT_TO_GITHUB_GIST}}
                                  </button>
                                </div>
                              </div>
                              <div class="settings-title">
                                <span>{{_RECOVER_PLAYLIST}}</span>
                              </div>
                              <div class="settings-content">
                                <p>{{_RECOVER_WARNING}}</p>
                                <label class="upload-button" for="my-file-selector">
                                  <input
                                    id="my-file-selector"
                                    type="file"
                                    style="display: none"
                                    ng-model="myuploadfiles"
                                    custom-on-change="importMySettings"
                                  />{{_RECOVER_FROM_LOCAL_FILE}}
                                </label>
                                <button
                                  class="btn btn-warning confirm-button"
                                  ng-show="githubStatus == 2"
                                  ng-click="showDialog(10)"
                                >
                                  {{_RECOVER_FROM_GITHUB_GIST}}
                                </button>
                              </div>
    
                              <div class="settings-title">
                                <span>{{_CONNECT_TO_GITHUB}}</span>
                              </div>
                              <div class="settings-content">
                                <div>
                                  <p>{{_STATUS}}：{{ githubStatusText }}</p>
                                  <button
                                    class="btn btn-primary confirm-button"
                                    ng-show="githubStatus == 0"
                                    ng-click="openGithubAuth(); showDialog(7);"
                                  >
                                    {{_CONNECT_TO_GITHUB}}
                                  </button>
                                  <button
                                    class="btn btn-warning confirm-button"
                                    ng-show="githubStatus == 1"
                                    ng-click="showDialog(7);"
                                  >
                                    {{_RECONNECT}}
                                  </button>
                                  <button
                                    class="btn btn-primary confirm-button"
                                    ng-show="githubStatus == 2"
                                    ng-click="GithubLogout();"
                                  >
                                    {{_CANCEL_CONNECT}}
                                  </button>
                                </div>
                              </div>
    
                              <div class="settings-title">
                                <span>{{_CONNECT_TO_LASTFM}}</span>
                              </div>
                              <div class="settings-content">
                                <div>
                                  <p>{{_STATUS}}：{{ lastfm.getStatusText() }}</p>
                                  <button
                                    class="btn btn-primary confirm-button"
                                    ng-show="!lastfm.isAuthRequested()"
                                    ng-click="lastfm.getAuth(); showDialog(4);"
                                  >
                                    {{_CONNECT_TO_LASTFM}}
                                  </button>
                                  <button
                                    class="btn btn-warning confirm-button"
                                    ng-show="lastfm.isAuthRequested() && !lastfm.isAuthorized()"
                                    ng-click="lastfm.getAuth(); showDialog(4);"
                                  >
                                    {{_RECONNECT}}
                                  </button>
                                  <button
                                    class="btn btn-primary confirm-button"
                                    ng-show="lastfm.isAuthRequested()"
                                    ng-click="lastfm.cancelAuth();"
                                  >
                                    {{_CANCEL_CONNECT}}
                                  </button>
                                </div>
                              </div>
                              <div class="settings-title">
                                <span>{{_SHORTCUTS}}</span>
                              </div>
                              <div class="settings-content">
                                <div class="shortcut_table">
                                  <div class="shortcut_table-header">
                                    <div class="shortcut_table-function">
                                      {{_SHORTCUTS_FUNCTION}}
                                    </div>
                                    <div class="shortcut_table-key">
                                      {{_SHORTCUTS}}
                                    </div>
                                    <div
                                      ng-if="!isChrome"
                                      class="shortcut_table-globalkey"
                                    >
                                      {{_GLOBAL_SHORTCUTS}}
                                    </div>
                                  </div>
                                  <div class="shortcut_table-line">
                                    <div class="shortcut_table-function">
                                      {{_PLAY_OR_PAUSE}}
                                    </div>
                                    <div class="shortcut_table-key">p</div>
                                    <div
                                      ng-if="!isChrome"
                                      class="shortcut_table-globalkey"
                                    >
                                      Ctrl(Cmd) + Alt + {{_KEYBOARD_SPACE}}
                                    </div>
                                  </div>
                                  <div class="shortcut_table-line">
                                    <div class="shortcut_table-function">
                                      {{_PREVIOUS_TRACK}}
                                    </div>
                                    <div class="shortcut_table-key">[</div>
                                    <div
                                      ng-if="!isChrome"
                                      class="shortcut_table-globalkey"
                                    >
                                      Ctrl(Cmd) + Alt + ←
                                    </div>
                                  </div>
                                  <div class="shortcut_table-line">
                                    <div class="shortcut_table-function">
                                      {{_NEXT_TRACK}}
                                    </div>
                                    <div class="shortcut_table-key">]</div>
                                    <div
                                      ng-if="!isChrome"
                                      class="shortcut_table-globalkey"
                                    >
                                      Ctrl(Cmd) + Alt + →
                                    </div>
                                  </div>
                                  <div class="shortcut_table-line">
                                    <div class="shortcut_table-function">
                                      {{_VOLUME_UP}}
                                    </div>
                                    <div class="shortcut_table-key">u</div>
                                    <div
                                      ng-if="!isChrome"
                                      class="shortcut_table-globalkey"
                                    >
                                      {{_SHORTCUTS_NOT_SET}}
                                    </div>
                                  </div>
                                  <div class="shortcut_table-line">
                                    <div class="shortcut_table-function">
                                      {{_VOLUME_DOWN}}
                                    </div>
                                    <div class="shortcut_table-key">d</div>
                                    <div
                                      ng-if="!isChrome"
                                      class="shortcut_table-globalkey"
                                    >
                                      {{_SHORTCUTS_NOT_SET}}
                                    </div>
                                  </div>
                                  <!-- <div class="shortcut_table-line">
                                    <div class="shortcut_table-function">
                                      静音/取消静音
                                    </div>
                                    <div class="shortcut_table-key">m</div>
                                    <div ng-if="!isChrome" class="shortcut_table-globalkey">
                                      全局快捷键
                                    </div>
                                  </div> -->
                                  <div class="shortcut_table-line">
                                    <div class="shortcut_table-function">
                                      {{_QUICK_SEARCH}}
                                    </div>
                                    <div class="shortcut_table-key">f</div>
                                    <div
                                      ng-if="!isChrome"
                                      class="shortcut_table-globalkey"
                                    >
                                      {{_SHORTCUTS_NOT_SET}}
                                    </div>
                                  </div>
                                  <div class="shortcut_table-line">
                                    <div class="shortcut_table-function">
                                      {{ZOOM_IN_OUT}}
                                    </div>
                                    <div class="shortcut_table-key">
                                      Ctrl(Cmd) + +/-
                                    </div>
                                    <div
                                      ng-if="!isChrome"
                                      class="shortcut_table-globalkey"
                                    >
                                      {{_SHORTCUTS_NOT_SET}}
                                    </div>
                                  </div>
                                  <!-- <div class="shortcut_table-line">
                                    <div class="shortcut_table-function">
                                      打开/关闭播放列表
                                    </div>
                                    <div class="shortcut_table-key">l</div>
                                    <div ng-if="!isChrome" class="shortcut_table-globalkey">
                                      全局快捷键
                                    </div>
                                  </div>
                                  <div class="shortcut_table-line">
                                    <div class="shortcut_table-function">
                                      切换播放模式
                                    </div>
                                    <div class="shortcut_table-key">s</div>
                                    <div ng-if="!isChrome" class="shortcut_table-globalkey">
                                      全局快捷键
                                    </div>
                                  </div> -->
                                </div>
                                <div
                                  class="shortcut"
                                  ng-if="!isChrome"
                                  class="btn btn-primary confirm-button"
                                >
                                  <svg
                                    class="feather"
                                    ng-show="!enableGlobalShortCut"
                                    ng-click="applyGlobalShortcut(true)"
                                  >
                                    <use href="#square"></use>
                                  </svg>
                                  <svg
                                    class="feather"
                                    ng-show="enableGlobalShortCut"
                                    ng-click="applyGlobalShortcut(true)"
                                  >
                                    <use href="#check-square"></use>
                                  </svg>
                                  {{_GLOBAL_SHORTCUTS_NOTICE}}
                                </div>
                              </div>
                              <div class="settings-title" ng-if="!isChrome">
                                <span>{{_PROXY_CONFIG}}</span>
                              </div>
                              <div class="settings-content" ng-if="!isChrome">
                                <span>{{_PROXY_CONFIG}}:</span>
                                {{proxyMode.displayText}}
                                <span ng-show="proxyMode.name=='custom'"
                                  >{{proxyRules}}</span
                                >
                                <button ng-click="showDialog(12)">
                                  {{_MODIFY}}
                                </button>
                              </div>
                              <div class="settings-title">
                                <span>{{_ABOUT}}</span>
                              </div>
                              <div class="settings-content">
                                <p>
                                  Listen 1 {{_HOMEPAGE}}:
                                  <a
                                    open-url="'https://listen1.github.io/listen1/'"
                                  >
                                    https://listen1.github.io/listen1/
                                  </a>
                                </p>
                                <p>Listen 1 {{_EMAIL}}: <EMAIL></p>
                                <p>
                                  {{_FEEDBACK}}:
                                  <a
                                    ng-if="isChrome"
                                    open-url="'https://github.com/listen1/listen1_chrome_extension/issues'"
                                    >https://github.com/listen1/listen1_chrome_extension/issues</a
                                  >
                                  <a
                                    ng-if="!isChrome"
                                    open-url="'https://github.com/listen1/listen1_desktop/issues'"
                                    >https://github.com/listen1/listen1_desktop/issues</a
                                  >
                                </p>
                                <p>{{_DESIGNER}} ({{_THEME_WHITE}}): iparanoid </p>
                                <p>{{_DESIGNER}} ({{_THEME_MODERN_BLACK}}, {{_THEME_MODERN_WHITE}}): 814959822, Antion</p>
                                <p>{{_VERSION}}: v2.33.0 {{_LICENSE_NOTICE}}</p>
                                <p ng-show='lastestVersion!=""'>
                                  {{_LATEST_VERSION}}: {{lastestVersion}}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- content page: 登录 -->
                        <div
                          class="page"
                          ng-show="current_tag==5 && is_window_hidden==1"
                        >
                          <div class="login">
                            <div ng-repeat="source in loginSourceList">
                              <div ng-show="is_login(source)">
                                <div class="usercard">
                                  <img ng-src="{{musicAuth[source].avatar}}" />
                                  <div class="usercard-title">
                                    <div class="usercard-nickname">
                                      {{musicAuth[source].nickname}}
                                    </div>
                                    <div class="usercard-info">{{source}}</div>
                                  </div>
                                  <button ng-click="logout(source)">
                                    {{_LOGOUT}}
                                  </button>
                                </div>
                              </div>
                              <div ng-show="!is_login(source)">
                                <div class="usercard">
                                  <img src="images/placeholder.png" />
    
                                  <div class="usercard-title">
                                    <div class="usercard-nickname">
                                      {{_NOT_LOGIN_NICKNAME}}
                                    </div>
                                    <div class="usercard-info">{{source}}</div>
                                  </div>
    
                                  <button
                                    ng-click=" openLogin(source);showDialog(11, source);"
                                  >
                                    {{_LOGIN}}
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
    
                        <!-- track list window-->
                        <div class="page">
                          <div
                            class="playlist-detail"
                            ng-show="is_window_hidden!=1 && window_type=='list'"
                          >
                            <div class="detail-head">
                              <div class="detail-head-cover">
                                <img
                                  ng-src="{{ cover_img_url }}"
                                  err-src="https://y.gtimg.cn/mediastyle/global/img/singer_300.png"
                                />
                              </div>
                              <div class="detail-head-title">
                                <h2>{{ playlist_title }}</h2>
                                <div class="playlist-button-list">
                                  <div class="playlist-button playadd-button">
                                    <div
                                      class="play-list"
                                      ng-click="playMylist(list_id)"
                                    >
                                      <span class="icon li-play-s"></span>
                                      {{_PLAY_ALL}}
                                    </div>
                                    <div
                                      class="add-list"
                                      ng-click="addMylist(list_id)"
                                    >
                                      <span class="icon li-add"></span>
                                    </div>
                                  </div>
                                  <div
                                    class="playlist-button clone-button"
                                    ng-show="is_local"
                                    ng-click="addLocalMusic(list_id)"
                                  >
                                    <div class="play-list">
                                      <span class="icon li-songlist"></span
                                      ><span>{{_ADD_LOCAL_SONGS}}</span>
                                    </div>
                                  </div>
                                  <div
                                    class="playlist-button clone-button"
                                    ng-show="!is_mine && !is_local"
                                    ng-click="clonePlaylist(list_id)"
                                  >
                                    <div class="play-list">
                                      <span class="icon li-songlist"></span
                                      ><span>{{_ADD_TO_PLAYLIST}}</span>
                                    </div>
                                  </div>
                                  <div
                                    class="playlist-button edit-button"
                                    ng-show="is_mine && !is_local"
                                    ng-click="showDialog(3, {list_id: list_id, playlist_title: playlist_title, cover_img_url: cover_img_url})"
                                  >
                                    <div class="play-list">
                                      <svg class="feather">
                                        <use href="#edit"></use>
                                      </svg>
                                      <span>{{_EDIT}}</span>
                                    </div>
                                  </div>
                                  <div
                                    class="playlist-button fav-button"
                                    ng-show="!is_mine && !is_local"
                                    ng-click="favoritePlaylist(list_id)"
                                  >
                                    <div
                                      class="play-list"
                                      ng-class="{'favorited':is_favorite,'notfavorite':!is_favorite}"
                                    >
                                      <svg class="feather">
                                        <use href="#star"></use>
                                      </svg>
                                      <span
                                        >{{is_favorite?_FAVORITED:_FAVORITE}}</span
                                      >
                                    </div>
                                  </div>
                                  <div
                                    class="playlist-button edit-button"
                                    ng-show="isChrome && is_favorite && !is_local"
                                    ng-click="closeWindow();showPlaylist(list_id)"
                                  >
                                    <div class="play-list">
                                      <span class="icon li-loop"></span
                                      ><span>{{_REFRESH_PLAYLIST}}</span>
                                    </div>
                                  </div>
                                  <div
                                    class="playlist-button edit-button"
                                    ng-show="!is_mine && !is_local"
                                    open-url="playlist_source_url"
                                  >
                                    <div class="play-list">
                                      <span class="icon li-link"></span
                                      ><span>{{_ORIGIN_LINK}}</span>
                                    </div>
                                  </div>
                                  <div
                                    class="playlist-button edit-button"
                                    ng-show="is_mine && !is_local"
                                    ng-click="showDialog(6)"
                                  >
                                    <div class="play-list">
                                      <svg class="feather">
                                        <use href="#git-merge"></use>
                                      </svg>
                                      <span>{{_IMPORT}}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
    
                            <ul class="detail-songlist">
                              <div class="playlist-search">
                                <svg class="feather playlist-search-icon">
                                  <use href="#search"></use>
                                </svg>
                                <svg
                                  class="feather playlist-clear-icon"
                                  ng-show="playlistFilter.key!=''"
                                  ng-click="clearFilter()"
                                >
                                  <use href="#x"></use>
                                </svg>
                                <input
                                  class="playlist-search-input"
                                  type="text"
                                  ng-model="playlistFilter.key"
                                  placeholder="{{_SEARCH_PLAYLIST}}"
                                />
                              </div>
                              <li class="head">
                                <div class="title">
                                  <a>{{_SONGS + '(' + songs.length + ')'}}</a>
                                </div>
                                <div class="artist"><a>{{_ARTISTS}}</a></div>
                                <div class="album"><a>{{_ALBUMS}}</a></div>
                                <div class="tools">{{_OPERATION}}</div>
                              </li>
                              <li
                                ng-repeat="song in songs | filter: fieldFilter track by $index"
                                ng-class-odd="'odd'"
                                ng-class-even="'even'"
                                ng-mouseenter="options=true"
                                ng-mouseleave="options=false"
                                ng-dblclick="addAndPlay(song)"
                                draggable="true"
                                drag-drop-zone
                                drag-zone-object="song"
                                drag-zone-title="song.title"
                                sortable="is_mine || is_local"
                                drag-zone-type="'application/listen1-song'"
                                drop-zone-ondrop="onPlaylistSongDrop(list_id, song, arg1, arg2, arg3)"
                              >
                                <div class="title">
                                  <!-- <a class="disabled" ng-if="song.disabled" ng-click="copyrightNotice()">{{ song.title }}</a> -->
                                  <a add-and-play="song">{{ song.title }}</a>
                                </div>
                                <div class="artist">
                                  <a ng-click="showPlaylist(song.artist_id)"
                                    >{{ song.artist }}</a
                                  >
                                </div>
                                <div class="album">
                                  <a ng-click="showPlaylist(song.album_id)"
                                    >{{ song.album }}</a
                                  >
                                </div>
                                <div class="tools">
                                  <a
                                    title="{{_ADD_TO_QUEUE}}"
                                    class="detail-add-button"
                                    add-without-play="song"
                                    ng-show="options"
                                    ><span class="icon li-add"></span
                                  ></a>
                                  <a
                                    title="{{_ADD_TO_PLAYLIST}}"
                                    class="detail-fav-button"
                                    ng-show="options"
                                    ng-click="showDialog(0, song)"
                                    ><span class="icon li-songlist"></span
                                  ></a>
                                  <a
                                    title="{{_REMOVE_FROM_PLAYLIST}}"
                                    class="detail-delete-button"
                                    ng-click="removeSongFromPlaylist(song, list_id)"
                                    ng-show="options && (is_mine=='1'||is_local) "
                                    ><span class="icon li-del"></span
                                  ></a>
                                  <a
                                    title="{{_ORIGIN_LINK}}"
                                    class="source-button"
                                    open-url="song.source_url"
                                    ng-show="options && !is_local"
                                    ><span class="icon li-link"></span
                                  ></a>
                                </div>
                              </li>
                            </ul>
                          </div>
                          <!-- now playing window-->
                          <div
                            class="songdetail-wrapper"
                            ng-class="{slidedown: window_type!=='track', coverbg: enableNowplayingCoverBackground}"
                          >
                            <div class="draggable-zone"></div>
                            <div
                              ng-if="enableNowplayingCoverBackground"
                              class="bg"
                              ng-style="{'background-image': 'url(' + currentPlaying.img_url + ')'}"
                            ></div>
                            <div
                              class="translate-switch"
                              ng-click="toggleLyricTranslation()"
                              ng-class="{selected: enableLyricTranslation}"
                            >
                              译
                            </div>
                            <div
                              class="close"
                              ng-class="isMac? 'mac':'' "
                              ng-click="popWindow()"
                            >
                              <svg class="icon">
                                <use href="#chevron-down"></use>
                              </svg>
                            </div>
    
                            <div ng-if="!isChrome && !isMac" class="window-control">
                              <svg class="icon" window-control="window_min">
                                <use href="#minimize-2"></use>
                              </svg>
                              <svg class="icon" window-control="window_max">
                                <use href="#maximize"></use>
                              </svg>
                              <svg class="icon" window-control="window_close">
                                <use href="#x"></use>
                              </svg>
                            </div>
    
                            <div class="playsong-detail">
                              <div class="detail-head">
                                <div class="detail-head-cover">
                                  <img
                                    ng-src="{{ currentPlaying.img_url  }}"
                                    err-src="https://y.gtimg.cn/mediastyle/global/img/album_300.png"
                                  />
                                </div>
                                <div class="detail-head-title">
                                  <!--<a title="加入收藏" class="clone" ng-click="showDialog(0, currentPlaying)">收藏</a>
                    <a open-url="currentPlaying.source_url" title="原始链接" class="link">原始链接</a>-->
                                </div>
                              </div>
                              <div class="detail-songinfo">
                                <div class="title">
                                  <h2>{{ currentPlaying.title }}</h2>
                                  <span
                                    class="badge"
                                    ng-if="enableNowplayingBitrate && currentPlaying.bitrate !== undefined"
                                    >{{ currentPlaying.bitrate }}</span
                                  >
                                  <span
                                    class="badge platform"
                                    ng-if="enableNowplayingPlatform && currentPlaying.platform !== undefined"
                                    >{{ currentPlaying.platformText }}</span
                                  >
                                </div>
                                <div class="info">
                                  <div class="singer">
                                    <span>{{_ARTIST}}： </span
                                    ><a
                                      ng-click="showPlaylist(currentPlaying.artist_id)"
                                      title="{{currentPlaying.artist}}"
                                      >{{ currentPlaying.artist }}</a
                                    >
                                  </div>
                                  <div class="album">
                                    <span>{{_ALBUM}}： </span
                                    ><a
                                      ng-click="showPlaylist(currentPlaying.album_id)"
                                      title="{{currentPlaying.album}}"
                                      >{{ currentPlaying.album }}</a
                                    >
                                  </div>
                                </div>
                                <div class="lyric">
                                  <div class="placeholder"></div>
                                  <p
                                    ng-repeat="line in lyricArray track by $index"
                                    data-line="{{line.lineNumber}}"
                                    ng-class="{ 'highlight': (line.lineNumber == lyricLineNumber) || (line.lineNumber == lyricLineNumberTrans) , hide: (line.translationFlag && !enableLyricTranslation), translate: line.translationFlag}"
                                  >
                                    {{ line.content }}
                                  </p>
                                  <div class="placeholder"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="footer">
                    <div class="left-control">
                      <span class="icon li-previous" prev-track=""></span>
                      <span
                        class="icon li-play play"
                        ng-class="isPlaying? 'li-pause': 'li-play'"
                        play-pause-toggle=""
                      ></span>
                      <span class="icon li-next" next-track=""></span>
                    </div>
                    <div class="main-info">
                      <div class="logo-banner" ng-if="playlist.length == 0">
                        <svg
                          class="logo"
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="#666666"
                          stroke="#666666"
                          stroke-width="1"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <polygon
                            points="7 4 7 19 16 19 16 16 10 16 10 4"
                          ></polygon>
                          <polygon points="13 4 13 13 16 13 16 4"></polygon>
                        </svg>
                      </div>
                      <div
                        class="cover"
                        class="cover"
                        ng-if="playlist.length > 0"
                        ng-click="toggleNowPlaying()"
                      >
                        <img
                          ng-src="{{ currentPlaying.img_url }}"
                          err-src="https://y.gtimg.cn/mediastyle/global/img/album_300.png"
                        />
                        <!-- Switching icon when hover -->
                        <div class="mask">
                          <svg class="feather" ng-if="getCurrentUrl() != '/now_playing'">
                            <use href="#chevrons-up"></use>
                          </svg>
                          <svg class="feather" ng-if="getCurrentUrl() === '/now_playing'">
                            <use href="#chevrons-down"></use>
                          </svg>
                        </div>
                      </div>
                      <div class="detail" ng-if="playlist.length > 0">
                        <div class="ctrl">
                          <a
                            ng-click="showDialog(0, currentPlaying)"
                            title="{{_ADD_TO_PLAYLIST}}"
                          >
                            <span class="icon li-songlist"></span>
                          </a>
                          <a
                            title="{{ settings.playmode | playmode_title }}(s)"
                            ng-click="changePlaymode()"
                          >
                            <span
                              ng-show="settings.playmode == 0"
                              class="icon li-loop"
                            ></span>
                            <span
                              ng-show="settings.playmode == 1"
                              class="icon li-random-loop"
                            ></span>
                            <span
                              ng-show="settings.playmode == 2"
                              class="icon li-single-cycle"
                            ></span>
                          </a>
                        </div>
    
                        <div class="title">
                          <span
                            ng-if="currentPlaying.source === 'xiami'"
                            style="color: orange; font-size: medium"
                            >⚠️ </span
                          >{{ currentPlaying.title }}
                        </div>
                        <div class="more-info">
                          <div class="current">{{ currentPosition }}</div>
                          <div class="singer">
                            <a ng-click="showPlaylist(currentPlaying.artist_id)"
                              >{{ currentPlaying.artist }}</a
                            >
                            -
                            <a ng-click="showPlaylist(currentPlaying.album_id)"
                              >{{ currentPlaying.album }}</a
                            >
                          </div>
                          <div class="total">{{ currentDuration }}</div>
                        </div>
                        <div class="playbar">
                          <div class="playbar-clickable">
                            <div
                              class="barbg"
                              id="progressbar"
                              mode="play"
                              draggable-bar=""
                            >
                              <div
                                class="cur"
                                ng-style="{width : myProgress + '%' }"
                              >
                                <span class="btn"><i></i></span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="right-control">
                      <div class="playlist-toggle">
                        <span
                          ng-click="togglePlaylist()"
                          class="icon li-list"
                        ></span>
                      </div>
                      <div class="volume-ctrl" volume-wheel="">
                        <span
                          class="icon"
                          ng-class="mute? 'li-mute': 'li-volume'"
                          ng-click="toggleMuteStatus()"
                        ></span>
                        <div class="m-pbar volume">
                          <div
                            class="barbg"
                            id="volumebar"
                            mode="volume"
                            draggable-bar=""
                          >
                            <div class="cur" ng-style="{width : volume + '%' }">
                              <span class="btn"><i></i></span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div ng-if="!isChrome" class="lyric-toggle">
                        <div
                          ng-click="openLyricFloatingWindow(true)"
                          class="lyric-icon"
                          ng-class="{'selected': enableLyricFloatingWindow}"
                        >
                          词
                        </div>
                      </div>
                    </div>
                    <div
                      class="menu-modal"
                      ng-class="{slideup: !menuHidden}"
                      ng-click="togglePlaylist()"
                    ></div>
                    <div class="menu" ng-class="{slideup: !menuHidden}">
                      <div class="menu-header">
                        <span class="menu-title"
                          >{{_TOTAL_SONG_PREFIX}}{{playlist.length}}{{_TOTAL_SONG_POSTFIX}}</span
                        >
                        <a class="add-all" ng-click="showDialog(0, playlist)">
                          <span
                            class="icon li-songlist"
                            ng-click="togglePlaylist()"
                          ></span>
                          <span>{{_ADD_TO_PLAYLIST}}</span></a
                        >
                        <a class="remove-all" clear-playlist=""
                          ><span
                            class="icon li-del"
                            ng-click="togglePlaylist()"
                          ></span
                          ><span>{{_CLEAR_ALL}}</span></a
                        >
    
                        <a class="close" ng-click="togglePlaylist()"
                          ><svg class="feather">
                            <use href="#x"></use>
                          </svg>
                        </a>
                      </div>
                      <ul class="menu-list">
                        <li
                          id="song{{ song.id }}"
                          ng-repeat="song in playlist track by $index"
                          ng-class="{ playing: currentPlaying.id == song.id }"
                          ng-mouseenter="playlist_highlight=true"
                          ng-mouseleave="playlist_highlight=false"
                          ng-dblclick="playById(song.id)"
                          ng-class-odd="'odd'"
                          ng-class-even="'even'"
                          draggable="true"
                          drag-drop-zone
                          drag-zone-object="song"
                          drag-zone-title="song.title"
                          sortable="true"
                          drag-zone-type="'application/listen1-song'"
                          drop-zone-ondrop="onCurrentPlayingSongDrop(song, arg1, arg2, arg3)"
                        >
                          <div class="song-status-icon">
                            <svg
                              class="feather play"
                              ng-show="currentPlaying.id == song.id"
                            >
                              <use href="#play"></use>
                            </svg>
                          </div>
                          <div
                            class="song-title"
                            ng-class="song.disabled? 'disabled':'' "
                          >
                            <a play-from-playlist="song"
                              ><span
                                ng-if="song.source === 'xiami'"
                                style="
                                  color: orange;
                                  border-radius: 12px;
                                  border: solid 1px;
                                  padding: 0 4px;
                                "
                                >⚠️ 🦐</span
                              >{{ song.title }}</a
                            >
                          </div>
                          <div class="song-singer">
                            <a
                              ng-click="showPlaylist(song.artist_id); togglePlaylist();"
                              >{{ song.artist }}</a
                            >
                          </div>
                          <div class="tools">
                            <span
                              remove-from-playlist="song"
                              data-index="{{$index}}"
                              ng-show="playlist_highlight"
                              class="icon li-del"
                            ></span>
                            <span
                              open-url="song.source_url"
                              ng-show="playlist_highlight"
                              class="icon li-link"
                            ></span>
                          </div>
                          <!-- <div class="song-time">00:00</div> -->
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="body" ng-if="theme==='white2' || theme==='black2' " >
        <div
        id="feather-container"
        style="visibility: hidden; position: absolute; width: 0px; height: 0px"
      ></div>
      <div
        ng-controller="PlayController as playCtrl"
        ng-init="loadLocalSettings()"
      >
        <div ng-controller="AuthController" ng-init="refreshAuthStatus()">
          <div >
            <div ng-controller="NavigationController">
              <div class="wrap">
                <!-- dialog-->
                <div class="shadow" ng-hide="is_dialog_hidden==1"></div>
                <div
                  class="dialog"
                  ng-hide="is_dialog_hidden==1"
                  ng-style="myStyle"
                >
                  <div class="dialog-header">
                    <span>{{ dialog_title }}</span
                    ><span class="dialog-close" ng-click="closeDialog()">×</span>
                  </div>
                  <div class="dialog-body">
                    <!-- choose playlist dialog-->
                    <ul class="dialog-playlist" ng-show="dialog_type==0">
                      <li class="detail-add" ng-click="newDialogOption(1)">
                        <img src="images/mycover.jpg" />
                        <h2>{{_CREATE_PLAYLIST}}</h2>
                      </li>
                      <li
                        ng-repeat="playlist in myplaylist track by $index"
                        ng-class-odd="'odd'"
                        ng-class-even="'even'"
                        ng-click="chooseDialogOption(playlist.info.id)"
                      >
                        <img err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png" ng-src="{{ playlist.info.cover_img_url }}" />
                        <h2>{{ playlist.info.title }}</h2>
                      </li>
                    </ul>
                    <!-- create new playlist dialog-->
                    <div class="dialog-newplaylist" ng-show="dialog_type==1">
                      <input
                        class="form-control"
                        type="text"
                        placeholder="{{_INPUT_NEW_PLAYLIST_TITLE}}"
                        ng-model="newlist_title"
                      />
                      <div class="buttons">
                        <button
                          class="btn btn-primary confirm-button"
                          ng-click="createAndAddPlaylist()"
                        >
                          {{_CONFIRM}}
                        </button>
                        <button
                          class="btn btn-default"
                          ng-click="cancelNewDialog(0)"
                        >
                          {{_CANCEL}}
                        </button>
                      </div>
                    </div>
                    <!-- edit playlist dialog-->
                    <div class="dialog-editplaylist" ng-show="dialog_type==3">
                      <div class="form-group">
                        <label>{{_PLAYLIST_TITLE}}</label>
                        <input
                          class="form-control"
                          type="text"
                          placeholder="{{_INPUT_PLAYLIST_TITLE}}"
                          ng-model="dialog_playlist_title"
                        />
                      </div>
                      <div class="form-group">
                        <label>{{_PLAYLIST_COVER_IMAGE_URL}}</label>
                        <input
                          class="form-control"
                          type="text"
                          placeholder="{{_INPUT_PLAYLIST_COVER_IMAGE_URL}}"
                          ng-model="dialog_cover_img_url"
                        />
                      </div>
                      <div class="buttons">
                        <button
                          class="btn btn-primary confirm-button"
                          ng-click="editMyPlaylist(list_id)"
                        >
                          {{_CONFIRM}}
                        </button>
                        <button class="btn btn-default" ng-click="closeDialog()">
                          {{_CANCEL}}
                        </button>
                      </div>
                      <div class="dialog-footer">
                        <button
                          class="btn btn-danger remove-button"
                          ng-click="removeMyPlaylist(list_id)"
                        >
                          {{_REMOVE_PLAYLIST}}
                        </button>
                      </div>
                    </div>
                    <div class="dialog-connect-lastfm" ng-show="dialog_type==4">
                      <p>{{_OPENING_LASTFM_PAGE}}</p>
                      <p>{{_CONFIRM_NOTICE_LASTFM}}</p>
                      <div class="buttons">
                        <button
                          class="btn btn-primary confirm-button"
                          ng-click="lastfm.updateStatus();closeDialog();"
                        >
                          {{_AUTHORIZED_FINISHED}}
                        </button>
                        <button
                          class="btn btn-warning warning-button"
                          ng-click="lastfm.getAuth();"
                        >
                          {{_AUTHORIZED_REOPEN}}
                        </button>
                      </div>
                    </div>
                    <!-- open playlist dialog-->
                    <div class="dialog-open-url" ng-show="dialog_type==5">
                      <div class="form-group">
                        <label>{{_PLAYLIST_LINK}}</label>
                        <input
                          class="form-control"
                          type="text"
                          placeholder="{{_EXAMPLE}}https://www.xiami.com/collect/198267231"
                          ng-model="dialog_url"
                        />
                      </div>
                      <div class="buttons">
                        <button
                          class="btn btn-primary confirm-button"
                          ng-click="openUrl(dialog_url);closeDialog();dialog_url='';"
                        >
                          {{_CONFIRM}}
                        </button>
                        <button class="btn btn-default" ng-click="closeDialog()">
                          {{_CANCEL}}
                        </button>
                      </div>
                    </div>
                    <ul class="dialog-merge-playlist" ng-show="dialog_type==6">
                      <li
                        ng-repeat="playlist in myplaylist track by $index"
                        ng-class-odd="'odd'"
                        ng-class-even="'even'"
                        ng-click="mergePlaylist(playlist.info.id)"
                      >
                        <img err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png" ng-src="{{ playlist.info.cover_img_url }}" />
                        <h2>{{ playlist.info.title }}</h2>
                      </li>
                    </ul>
                    <div class="dialog-connect-github" ng-show="dialog_type==7">
                      <p>{{_OPENING_GITHUB_PAGE}}</p>
                      <p>{{_CONFIRM_NOTICE_GITHUB}}</p>
                      <div class="buttons">
                        <button
                          class="btn btn-primary confirm-button"
                          ng-click="updateGithubStatus();closeDialog();"
                        >
                          {{_AUTHORIZED_FINISHED}}
                        </button>
                        <button
                          class="btn btn-warning warning-button"
                          ng-click="openGithubAuth();"
                        >
                          {{_AUTHORIZED_REOPEN}}
                        </button>
                      </div>
                    </div>
                    <ul class="dialog-backuplist" ng-show="dialog_type==8">
                      <li class="detail-add" ng-click="newDialogOption(9)">
                        <img err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png" src="images/mycover.jpg" />
                        <h2>{{_CREATE_PLAYLIST_BACKUP}}</h2>
                      </li>
                      <li
                        ng-repeat="backup in myBackup track by $index"
                        ng-class-odd="'odd'"
                        ng-class-even="'even'"
                        ng-click="backupMySettings2Gist(backup.id, backup.public); closeDialog();"
                      >
                        <img err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png" ng-src="images/mycover.jpg" />
                        <h2>
                          {{ backup.id }}<br />
                          {{backup.description}}
                        </h2>
                      </li>
                    </ul>
                    <!-- create new backup dialog-->
                    <div class="dialog-newbackup" ng-show="dialog_type==9">
                      <button
                        class="btn btn-primary confirm-button"
                        ng-click="backupMySettings2Gist(null, true);closeDialog();"
                      >
                        {{_CREATE_PUBLIC_BACKUP}}
                      </button>
                      <button
                        class="btn btn-primary confirm-button"
                        ng-click="backupMySettings2Gist(null, false);closeDialog();"
                      >
                        {{_CREATE_PRIVATE_BACKUP}}
                      </button>
                      <button
                        class="btn btn-default"
                        ng-click="cancelNewDialog(8)"
                      >
                        {{_CANCEL}}
                      </button>
                    </div>
                    <ul class="dialog-backuplist" ng-show="dialog_type==10">
                      <li
                        ng-repeat="backup in myBackup track by $index"
                        ng-class-odd="'odd'"
                        ng-class-even="'even'"
                        ng-click="importMySettingsFromGist(backup.id); closeDialog();"
                      >
                        <img err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png" ng-src="images/mycover.jpg" />
                        <h2>{{ backup.id }} {{backup.description}}</h2>
                      </li>
                    </ul>
                    <div class="dialog-open-login" ng-show="dialog_type==11">
                      <p>{{_LOGIN_DIALOG_NOTICE}}</p>
                      <div class="buttons">
                        <button
                          class="btn btn-primary confirm-button"
                          ng-click="closeDialog();refreshAuthStatus();"
                        >
                          {{_LOGIN_SUCCESS}}
                        </button>
                        <button
                          class="btn btn-warning warning-button"
                          ng-click="openLogin(dialog_data);"
                        >
                          {{_LOGIN_FAIL_RETRY}}
                        </button>
                      </div>
                    </div>
                    <div class="dialog-proxy" ng-show="dialog_type==12">
                      <select
                        ng-options="mode.displayText for mode in proxyModes"
                        ng-model="proxyModeInput"
                        ng-change="changeProxyMode(proxyModeInput)"
                      ></select>
                      <div
                        ng-show="proxyModeInput.name=='custom'"
                        class="custom-proxy"
                      >
                        <div class="rule-input">
                          <div class="field-name">{{_PROTOCOL}}</div>
                          <select
                            ng-options="protocol for protocol in proxyProtocols"
                            ng-model="proxyProtocol"
                            ng-change="changeProxyProtocol(proxyProtocol)"
                          ></select>
                          <div class="field-name">{{_HOST}}</div>
                          <input type="text" id="proxy-rules-host" />
                          <div class="field-name">{{_PORT}}</div>
                          <input type="text" id="proxy-rules-port" />
                        </div>
                      </div>
                      <div class="buttons">
                        <button
                          class="btn btn-primary confirm-button"
                          ng-click="setProxyConfig();closeDialog();"
                        >
                          {{_CONFIRM}}
                        </button>
                        <button
                          class="btn btn-warning warning-button"
                          ng-click="closeDialog();"
                        >
                          {{_CANCEL}}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="main" ng-controller="MyPlayListController">
                  <div class="sidebar">
                    <div class="flex-scroll-wrapper">
                      <div class="menu-control"></div>
                        <div ng-class="{'opensidebar':isOpenSidebar,footerdef:playlist.length == 0}" class="sidebar-content">
                          <div class="logo-content" ng-click="openSidebar()">
                            <div class="logo-svg" >
                              <svg xmlns="http://www.w3.org/2000/svg" width="512" height="512.024" viewBox="0 0 780.964 781">
                                <path fill="currentColor" d="M415,414H595v594H907v186H415V414Zm283,0H884V910H698V414Z" transform="translate(-270.518 -413.5)"/>
                              </svg>
                          </div>
                            <div class="logo-title">
                              <svg xmlns="http://www.w3.org/2000/svg" width="1591.16" height="228" viewBox="0 0 1591.16 228">
                                <path fill="currentColor" d="M1188,564.251c44.35-.743,73.87,8.929,92,34.07,4.63,6.417,10.28,12.646,11,23.047h-2c-6.83,4.791-27.29,6.16-36,9.019-2.99.981-9.55,1.433-10,1-9.12-9.946-15.51-19.273-31-23.047-22.89-5.579-52.89,6.9-49,26.053,26.63,15.944,65.56,19.033,95,32.067,7.96,3.523,14.47,10.142,21,15.031,13.9,10.41,27.3,34.3,17,59.122-14.37,34.647-71.81,61.089-125,46.1-32.66-9.206-55.68-23.2-67-54.112l49-11.023c12.66,19.852,89.26,30.411,89-7.014-33.95-23.62-85.94-18.965-116-49.1-13.15-13.182-19.73-32.145-11-55.114,3.41-8.97,8.01-17.794,15-23.048,9.43-7.087,20.78-14.024,33-18.037C1170.43,566.821,1181.44,568.262,1188,564.251Zm916,0c13.52-.17,27.96-1.1,39,2,43.32,12.169,70.17,39.15,83,82.169,3.47,11.651,6.73,32.921,3,48.1q-3,11.02-6,22.045c-16.56,39.277-46.84,60.978-93,71.147-20.08,4.423-46.78-4.225-58-10.021-31.59-16.312-52.46-36.926-64-73.151-1.76-5.533-.61-9.837-2-16.033-1.97-8.8-2.22-24.978,0-34.07l6-21.044c10.93-26.763,32.77-50.773,59-62.128l21-7.014C2096.14,565.107,2100.88,566.21,2104,564.251Zm-793,3.006h207v50.1h-78V786.71h-50V617.36h-79v-50.1Zm235,0h172v50.1H1596v36.075h103v47.1H1596v36.074h122v50.1H1546V567.257Zm210,0h56c5.11,8.269,12.51,15.258,18,23.047,19.14,27.184,38.64,53.182,58,80.166,9.45,13.175,22.36,25.422,30,40.083h1v-143.3h50V786.71h-57c-6.61-13.269-18.53-24.292-27-36.075-16.45-22.885-32.55-45.215-49-68.14-7.56-10.538-24.74-27.126-29-39.081h-1v143.3h-50V567.257Zm509,0h56c5.11,8.269,12.51,15.258,18,23.047,19.14,27.184,38.64,53.182,58,80.166,9.45,13.175,22.36,25.422,30,40.083h1v-143.3h50V786.71h-57c-6.61-13.269-18.53-24.292-27-36.075-16.45-22.885-32.55-45.215-49-68.14-7.56-10.538-24.74-27.126-29-39.081h-1v143.3h-50V567.257Zm257,0h172v50.1H2572v36.075h103v47.1H2572v36.074h122v50.1H2522V567.257Zm-414,47.1c-5.43,3.5-13.32,3.046-19,6.012-14.03,7.333-26.79,22-32,38.079-12.54,38.754,15.2,67.758,40,77.159,6.39,2.423,18.33,6.548,29,4.009,23.88-5.682,41.36-18.317,50-39.081,15.97-38.4-11.38-72.647-37-83.172C2130.67,613.938,2119.55,614.2,2108,614.354Z" transform="translate(-1103.34 -563.5)"/>
                              </svg>
                              
                            </div>
                          </div>
                          <div class="sidebar-scroll-content">
                            <div class="menu-title">
                        <div ng-class="{'opensidebar':isOpenSidebar}" class="title">{{_PLATFORM_UNION}}</div>   
                        
                      </div>
                            <ul class="nav masthead-nav">
                        <li
                          ng-class="{ 'active':(current_tag==2) && (window_url_stack.length ==0) }"
                          ng-click="showTag(2)"
                        >
                          <div ng-class="{'opensidebar':isOpenSidebar}" class="sidebar-block">
                            <span class="icon li-featured-list"></span
                            ><a>{{_PLAYLISTS}}</a>
                          </div>
                        </li>
                            </ul>
                            <div
                        ng-if="!isChrome || is_login('netease') || is_login('qq')"
                        class="menu-title"
                      >
                        <div ng-class="{'opensidebar':isOpenSidebar}" class="title">{{_MY_MUSIC}}</div>                     
                      </div>
                            <ul class="nav masthead-nav">
                        <li
                          ng-if="!isChrome"
                          ng-click="showPlaylist('lmplaylist_reserve')"
                          ng-class="{ 'active':window_type=='list' && ( '/playlist?list_id=lmplaylist_reserve' === getCurrentUrl() ) }"
                        >
                          <div ng-class="{'opensidebar':isOpenSidebar}" class="sidebar-block">
                            <div>
                            <svg t="1659442896517"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1723" width="512" height="512"><path fill="currentColor" d="M9.25119901 889.06160075c0 69.37933454 56.30786571 125.68720026 125.68720024 125.68720024h754.1232015c69.37933454 0 125.68720026-56.30786571 125.68720024-125.68720024v-502.74880101H9.25119901v502.74880101z m879.81040174-754.1232015H449.05585012A125.68720026 125.68720026 0 0 0 323.36864987 9.25119901H135.03894901C65.55906473 9.25119901 9.25119901 65.55906473 9.25119901 134.93839925v188.63135015h1005.49760198v-62.84360014c0-69.37933454-56.30786571-125.68720026-125.68720024-125.78775001z" p-id="1724"></path></svg>
                          </div> 
                            <a>{{_LOCAL_MUSIC}}</a>
                          </div>
                        </li>
                        <li
                          ng-if="is_login('netease')"
                          ng-click="showTag(6, {platform:'netease', user: musicAuth.netease});"
                          ng-class="{ 'active':(current_tag==6 && tag_params.platform=='netease') && (window_url_stack.length ==0) }"
                        >
                          <div ng-class="{'opensidebar':isOpenSidebar}" class="sidebar-block">
                            <div>
                          <svg t="1659438696222" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1718" width="512" height="512"><path d="M984.82626711 518.27221813v314.72202442c0 62.83016817-51.40650127 126.23151979-102.24181922 151.93477031-28.55916739 14.27958373-58.8318847 18.27786711-90.81815219 18.27786717-199.34298832-0.57118343-399.25715974-0.57118343-598.60014792-0.57118341-98.8147191 0-181.06512109-90.24696889-180.49393773-180.49393775 1.14236668-205.62600507 0.57118343-411.25200997 0.57118334-617.44919836 0-66.82845164 47.4082179-130.22980311 105.6689192-157.64660389 26.84561727-12.56603358 54.83360135-17.70668378 84.53513544-17.70668381 201.62772161 0.57118343 403.25544315 0.57118343 604.8831648 0.57118335 69.11318498 0 131.37216986 46.83703452 159.93133722 105.09773601 13.13721707 26.84561727 17.70668378 54.83360135 17.70668374 84.5351353-1.7135501 99.95708581-1.14236668 199.34298832-1.14236668 298.72889066z m-358.13195879-108.52483609c1.14236668 0 4.56946676 0.57118343 7.42538351 0.57118345 2.85591675 0.57118343 5.71183343 0.57118343 7.99656689 2.28473345 48.5505845 25.70325061 85.10631875 64.54371822 96.52998569 118.23495291 13.70840029 65.68608496 0.57118343 127.37388647-44.5523011 179.92275434-72.54028509 84.53513538-206.76837169 105.09773596-305.01190745 53.69123456-106.81128602-55.40478468-171.35500425-197.05825477-124.51796969-319.29149101 23.98970064-62.83016817 61.11661813-113.09430275 122.23323632-143.93820357 15.99313367-7.9965669 33.12863417-15.42195035 47.97940128-25.70325061 19.99141721-13.70840029 23.41851719-35.98455091 11.4236668-55.97596809-10.2813003-17.70668378-34.27100084-25.70325061-53.12005125-18.27786708-109.0960193 42.83875106-181.06512109 118.80613625-217.62085533 231.32925566-26.84561727 83.39276864-17.13550042 162.78725409 16.56431709 241.03937253 23.98970064 54.83360135 60.54543483 99.95708581 109.09601931 137.08400339 39.98283428 30.27271734 83.39276864 50.83531791 130.80098651 61.68780143 75.96738523 17.13550042 150.22122028 6.85420015 221.61913876-25.13206727 126.80270307-56.54715136 212.48020515-202.77008831 166.78553744-350.13539188-24.56088395-78.82330201-76.53856848-132.51453654-150.79240368-165.07198726-23.98970064-10.2813003-50.26413461-15.42195035-77.68093528-22.8473339-2.28473334-17.70668378-6.28301684-37.12691754-7.42538348-56.54715145-1.14236668-24.56088395 22.84733386-39.41165101 45.12348441-29.13035063 11.42366688 5.14065014 22.27615056 12.56603358 33.12863422 18.84905041 30.27271734 17.70668378 58.2607014 10.85248359 69.68436843-18.27786716 6.28301684-16.56431707-1.14236668-42.26756771-19.9914172-53.69123455-20.56260047-12.56603358-43.40993431-21.70496722-65.68608503-31.41508414-51.40650127-22.84733386-118.23495283 14.27958373-144.50938688 60.54543471-17.70668378 30.84390082-13.13721707 64.54371822-4.56946675 97.10116907 2.85591675 11.99485033 11.42366688 25.13206727-6.28301677 33.1286342-8.56775021 3.99828343-17.13550042 7.42538352-25.70325061 11.42366692-77.10975185 33.12863417-123.37560301 134.79926988-101.09945263 218.19203866 19.42023388 71.96910175 105.66891925 132.51453654 180.49393788 106.811286 47.4082179-15.99313367 108.524836-73.11146841 101.67063576-134.22808662-3.42710013-39.98283428-12.56603358-80.536852-19.99141717-122.23323647z" fill="#E00000" p-id="1719"></path><path d="M1219.58262286 856.98394298V190.98416012c31.98626742 19.42023388 66.25726829 13.13721707 98.8147191 13.13721695 154.79068717 1.14236668 310.15255761 0 464.94324469 1.14236676 23.41851719 0 47.4082179 5.71183343 69.68436837 12.5660336 28.55916739 8.56775021 38.26928428 33.12863417 39.98283432 60.54543488 1.7135501 25.70325061 0.57118343 51.9776846 0.57118329 78.25211852 0 133.65690317-1.14236668 267.31380653 0.57118338 401.54189319 1.14236668 77.10975185-29.1303507 91.38933559-90.8181522 98.81471896-33.12863417 3.99828343-66.82845164 0.57118343-99.95708583 0.57118352-6.28301684-25.70325061 1.14236668-34.84218421 25.13206732-37.12691767 19.42023388-1.7135501 39.41165101-5.71183343 57.68951808-11.42366702 13.13721707-3.99828343 22.27615056-13.70840029 22.2761505-30.84390081-0.57118343-161.64488734 0-323.86095793-0.57118336-485.50584507 0-23.98970064-18.27786711-39.98283428-46.8370344-39.98283429-57.11833477-0.57118343-113.66548614 0-170.78382091 0-88.53341879 0-177.06683766 0.57118343-265.60025647-0.57118328-17.13550042 0-22.27615056 5.14065014-22.27615055 22.27615047 0.57118343 187.34813795 0 374.69627575 1.14236672 562.0444137 0 18.84905043-5.14065014 23.98970064-23.41851732 22.27615064-19.42023388-2.85591675-39.41165101-1.7135501-60.54543473-1.71355019zM4658.67755692 317.78686327c26.27443397 0 50.83531791-0.57118343 74.82501848 0.57118327 3.42710013 0 9.13893356 8.56775021 9.13893361 13.70840045 0.57118343 37.12691754 1.14236668 74.25383516 0 111.38075265-0.57118343 17.70668378 3.99828343 23.98970064 22.84733384 23.41851716 70.82673508-1.14236668 141.08228678-0.57118343 211.90902183-0.57118337 29.70153417 0 43.40993431 13.70840029 45.12348442 46.26585119-4.56946676 0.57118343-9.13893356 1.14236668-13.13721696 1.14236673-82.25040197 0-164.50080404 0.57118343-246.75120609 0-15.42195035 0-19.99141721 5.14065014-19.99141704 19.99141719 0.57118343 79.39448523 0.57118343 159.36015393 0.57118328 238.75463917 0 58.8318847-17.70668378 79.96566864-75.39620187 84.53513527-37.69810095 2.85591675-75.39620181 0.57118343-113.09430271 0.57118352 5.71183343-31.4150841 11.99485033-35.41336751 42.83875107-38.26928439 15.42195035-1.14236668 31.98626742-4.56946676 45.12348436-11.99485023 8.56775021-5.14065014 14.85076698-20.56260047 14.85076707-30.8439008 1.14236668-82.25040197 0-164.50080404 0.57118332-246.75120595 0-15.99313367-9.71011687-15.42195035-20.56260047-15.42195044-57.68951801 0-115.37903619 0.57118343-173.06855422-0.57118334-17.13550042-0.57118343-35.98455091-3.42710013-51.40650123-10.85248356-25.70325061-11.42366688-35.98455091-37.12691754-34.84218428-62.83016808 4.56946676-81.10803537 11.99485033-162.21607061 18.84905052-245.03765618h119.37731971c153.0771371-1.14236668 306.15427413-1.7135501 458.66022775-3.42710003 14.27958373 0 16.56431707 6.85420015 16.56431705 17.13550043-0.57118343 10.85248359 2.28473334 23.98970064-16.56431705 24.56088392l-390.68940955 8.56775016c-29.70153417 0.57118343-58.8318847 0.57118343-88.53341882 0-10.85248359 0-15.99313367 2.85591675-17.13550039 14.85076705-3.42710013 58.2607014-7.9965669 115.95021947-12.56603362 174.21092089-2.28473334 29.70153417 3.42710013 35.41336751 32.55745073 35.98455085 45.12348447 0 90.81815222-0.57118343 135.94163665 0.57118339 18.27786711 0.57118343 23.41851719-6.28301684 22.8473339-23.98970055 0.57118343-41.69638436 1.14236668-82.82158541 1.14236671-125.66033637zM3644.25593211 504.56381782c24.56088395 1.7135501 46.26585106 3.99828343 67.97081829 3.99828339 129.08743647 0.57118343 258.17487296 0 386.69112624 0 23.41851719 0 46.83703452 1.14236668 69.6843683 2.85591677 36.55573423 2.28473334 62.25898494 29.1303507 62.83016814 72.5402851 1.7135501 85.67750212 0.57118343 171.35500425 1.14236676 257.03250631 0 14.85076698-8.56775021 15.99313367-19.99141715 15.99313359-71.39791843-0.57118343-142.79583693 0-214.19375529 0-87.39105211 0-174.21092095 0.57118343-261.60197302-0.57118318-21.70496722 0-43.40993431-3.99828343-65.11490159-7.42538362-15.42195035-2.85591675-28.55916739-26.84561727-28.55916737-51.97768449v-286.16285701c0.57118343-2.28473334 1.14236668-5.14065014 1.14236669-6.28301686z m293.5882405 200.48535484c-63.40135157 0-126.23151979 0.57118343-189.63287123 0-14.85076698 0-21.13378386 4.56946676-19.99141722 19.99141716 1.14236668 14.27958373 0 28.55916739 0.57118332 43.40993447 0 27.41680065 11.42366688 41.12520094 38.8404676 41.12520097 119.94850288 0.57118343 239.89700583 0.57118343 360.41669215 0.57118332 11.99485033 0 17.13550042-4.56946676 16.56431712-17.13550039-1.14236668-21.13378386-1.7135501-42.83875106 0-63.97253495 1.7135501-18.84905043-3.99828343-25.13206727-23.41851732-24.56088387-61.11661813 1.14236668-122.23323628 0.57118343-183.34985442 0.57118329z m-1.71355005-48.5505844c62.25898494 0 124.51796971-0.57118343 187.34813793 0.57118324 14.85076698 0 21.70496722-3.99828343 20.56260052-19.42023375-1.14236668-16.56431707 0-33.12863417 0-49.12176786 0-28.55916739-4.56946676-34.84218421-33.12863417-34.84218422-120.51968633-0.57118343-241.03937259-0.57118343-361.55905879-0.57118334-14.85076698 0-21.13378386 6.85420015-20.56260057 21.70496718 0.57118343 19.99141721 1.14236668 39.98283428 0 59.40306811-1.7135501 18.27786711 5.14065014 23.41851719 23.41851729 23.41851724 60.54543483-1.7135501 122.23323628-1.14236668 183.92103779-1.1423666zM1982.68357478 783.87247462c47.97940115-34.84218421 96.52998574-68.54200163 143.36702031-105.0977359 47.4082179-37.12691754 86.24868543-82.25040197 124.51796955-135.94163666-40.55401772 0-74.8250185-0.57118343-108.52483596 0.57118334-5.14065014 0-10.2813003 7.9965669-14.8507669 13.13721704-23.41851719 26.84561727-45.12348447 57.11833477-79.96566866 70.25555169-20.56260047 7.42538352-42.83875106 9.71011687-70.25555171 14.850767 27.98798398-27.98798398 55.40478468-47.4082179 71.96910165-74.25383514 16.56431707-26.84561727 22.27615056-59.97425148 32.55745093-91.9605188 23.98970064 26.84561727 57.11833477 22.27615056 88.53341876 22.27615044 142.22465355 0.57118343 283.87812362 1.7135501 426.10277703-0.57118338 60.54543483-1.14236668 95.9588023 34.84218421 106.81128601 90.24696888 10.2813003 54.26241805 7.9965669 108.524836-3.42710015 161.07370403-12.56603358 55.97596803-46.83703452 93.6740689-117.09258616 100.52826902-42.26756771 4.56946676-83.96395206 6.85420015-125.66033637 10.28130025-6.28301684-32.55745077-1.14236668-39.41165101 26.27443389-39.41165093 46.26585106-0.57118343 88.53341879-10.85248359 116.5214029-50.83531788 9.71011687-14.27958373 17.13550042-33.12863417 18.27786719-50.26413463 3.42710013-41.12520094 3.42710013-82.25040197 3.42710005-123.37560292 0-33.6998175-9.71011687-45.12348447-39.98283436-52.54886801-11.99485033-2.85591675-24.56088395-2.28473334-36.55573421-0.57118334-5.71183343 0.57118343-13.13721707 6.85420015-14.85076702 11.42366695-13.13721707 51.9776846-42.26756771 94.81643567-77.10975189 134.22808669-58.2607014 66.25726829-129.08743647 116.52140289-211.33783845 149.07885353-30.27271734 11.99485033-63.40135157 15.42195035-94.81643565 23.41851724-1.14236668-3.42710013-2.28473334-6.28301684-3.42710011-9.71011683 13.70840029-7.42538352 27.41680065-15.42195035 41.69638426-22.84733393 114.23666945-61.11661813 197.05825477-151.93477037 254.17658968-267.31380651 6.85420015-13.70840029 6.28301684-20.56260047-11.42366702-19.99141713-23.98970064 0.57118343-47.97940115-0.57118343-71.96910176 0.57118337-6.85420015 0.57118343-17.13550042 4.56946676-19.99141713 10.28130025-38.26928428 67.97081826-97.10116898 114.80785275-158.78897053 158.7889705-41.12520094 29.1303507-83.96395206 56.54715136-134.2280866 68.54200165-19.42023388 4.56946676-39.41165101 5.71183343-58.83188479 7.99656687-0.57118343 0-1.14236668-1.14236668-1.14236678-2.85591675zM3350.66769158 609.66155379c29.70153417 0 55.40478468-0.57118343 81.67921872 0.57118324 3.99828343 0 9.71011687 5.14065014 10.85248362 9.13893363 12.56603358 42.83875106 26.27443397 85.67750212 36.55573424 129.0874365 7.42538352 32.55745077-4.56946676 84.53513538-36.55573424 96.52998557-17.70668378 6.28301684-36.55573423 11.99485033-54.83360142 11.99485025-155.93305382 1.14236668-311.29492428 1.14236668-467.22797805 0-36.55573423-0.57118343-77.10975185-6.28301684-90.24696885-48.5505843-6.85420015-22.27615056-7.42538352-49.12176788-2.28473351-71.96910185 10.85248359-45.6946678 27.98798398-89.67578557 42.83875118-134.22808662 10.85248359-31.98626742 22.27615056-63.40135157 33.6998174-95.38761902 9.71011687-26.27443397 6.28301684-33.12863417-21.70496708-33.12863413-19.99141721 0-39.98283428 0.57118343-59.4030682 0-29.1303507-0.57118343-40.55401772-11.99485033-42.26756766-43.40993444 3.42710013-0.57118343 6.28301684-1.14236668 9.71011686-1.14236667h662.57268291c33.12863417 0 41.69638436 8.56775021 44.55230112 42.83875104-7.42538352 0.57118343-14.27958373 1.7135501-21.70496729 1.71355007-151.36358702 0-303.29835734 0.57118343-454.66194439-0.57118334-18.84905043 0-26.27443397 9.13893356-30.84390077 23.98970053-18.84905043 57.68951801-36.55573423 115.95021947-55.4047847 173.63973758-6.85420015 21.70496722-15.99313367 43.40993431-23.41851724 64.54371831-14.85076698 41.12520094 7.9965669 74.8250185 52.54886794 74.82501844 128.51625316 0.57118343 256.4613229 0 384.97757608 0.57118339 41.12520094 0 57.11833477-19.99141721 46.26585114-59.97425145-11.42366688-42.26756771-24.56088395-84.53513538-37.12691761-126.23151982-1.7135501-4.56946676-4.56946676-7.42538352-8.5677502-14.85076691zM2654.9663747 462.86743343c-161.0737039 0-319.29149122 0-477.50927839-0.57118331-17.70668378 0-35.98455091-1.7135501-51.97768462-7.99656693-22.84733386-9.13893356-32.55745077-31.98626742-33.12863424-54.26241795-2.28473334-68.54200163-0.57118343-137.08400337-0.57118326-205.62600496 0-1.14236668 1.14236668-2.28473334-0.57118342 0.57118325 19.42023388 2.85591675 37.12691754 7.42538352 54.83360141 7.42538355 130.80098659 1.14236668 261.60197303 0.57118343 392.97414292 2.28473343 25.70325061 0.57118343 51.9776846 6.85420015 77.68093518 13.13721692 26.84561727 6.28301684 37.12691754 30.27271734 38.26928442 52.54886801 1.7135501 63.97253491 0 127.37388647 0 192.48878799z m-280.45102359-111.38075272h-178.7803878c-6.28301684 0-14.85076698 1.14236668-18.277867 5.14065012-10.2813003 13.13721707-1.14236668 46.83703452 14.85076702 53.69123466 10.85248359 4.56946676 23.41851719 7.42538352 35.41336751 7.42538354 98.8147191 0.57118343 197.62943812 0.57118343 296.44415717 0.57118333 49.69295123 0 49.12176788 0 46.83703454-48.55058457-0.57118343-14.27958373-5.14065014-18.84905043-19.42023379-18.27786708-59.40306815 0.57118343-118.23495283 0-177.06683765 0z m-2.85591677-45.12348446c59.40306815 0 119.37731956 0 178.78038775-0.57118324 6.28301684 0 14.85076698-1.7135501 17.70668373-5.71183352 13.13721707-18.27786711-1.7135501-47.97940115-25.13206735-48.5505846-117.66376954-0.57118343-235.32753909-0.57118343-353.56249197 0-5.14065014 0-11.99485033 7.9965669-14.27958359 13.70840038-2.28473334 5.14065014 0 11.99485033-0.57118344 18.2778671-1.7135501 16.56431707 4.56946676 22.84733386 22.2761507 22.27615064 58.2607014 0 116.52140289 0.57118343 174.78210417 0.57118324zM3574.57156369 428.59643259h100.52826917c23.41851719 0 21.70496722 0.57118343 15.99313374-21.70496714-5.14065014-21.13378386-5.71183343-43.40993431-7.99656689-66.82845169h89.10460225v39.411651c0.57118343 32.55745077 17.13550042 49.12176788 48.55058442 49.12176783h234.18517239c29.70153417 0 49.69295123-19.42023388 50.26413462-49.12176783v-39.98283434h89.10460216c-3.99828343 23.98970064-7.42538352 46.26585106-11.99485015 68.54200171-2.85591675 13.13721707-2.85591675 21.70496722 14.27958357 21.13378382 21.70496722-0.57118343 43.98111773 0 65.68608493 0 29.1303507 0.57118343 38.84046764 12.56603358 34.27100081 43.98111777H3624.83569831c-38.84046764 0-42.83875106-3.42710013-50.26413462-44.55230113z" p-id="1720"></path><path d="M1338.38875907 327.49698011c21.13378386 0 40.55401772 1.14236668 59.40306816-0.57118326 12.56603358-0.57118343 13.70840029 3.42710013 15.42195036 14.85076698 4.56946676 42.83875106 11.42366688 85.10631875 17.70668379 127.37388646 0.57118343 2.85591675 3.42710013 5.14065014 9.13893352 14.27958366 15.42195035-53.69123465 23.41851719-103.38418587 25.70325065-154.79068716h76.53856852c-3.99828343 19.42023388-6.28301684 38.84046764-12.56603357 57.1183348-19.42023388 55.97596803-42.83875106 110.80956935-60.54543487 166.78553741-5.14065014 15.99313367 0 38.26928428 7.99656687 53.69123461 15.99313367 31.98626742 38.26928428 60.54543483 54.83360129 91.96051886 7.9965669 14.85076698 7.9965669 34.27100084 8.56775023 52.54886805-42.83875106-35.98455091-75.39620181-78.25211851-102.24181917-129.08743658-35.41336751 45.6946678-69.11318498 89.67578557-103.3841859 133.65690335-2.28473334-1.14236668-5.14065014-2.85591675-7.42538345-3.99828339 1.14236668-17.70668378-3.42710013-35.41336751 7.99656685-53.69123475 24.56088395-38.26928428 46.83703452-77.10975185 68.5420017-117.0925862 3.42710013-6.28301684 2.85591675-17.13550042 0.57118333-24.5608838-13.13721707-40.55401772-29.1303507-79.96566864-41.69638438-120.51968649-9.71011687-30.84390082-16.56431707-62.83016817-24.56088393-94.24525219v-13.70840036zM1777.62875312 754.17094063c-13.70840029-15.42195035-29.1303507-29.70153417-41.69638435-46.26585117-17.70668378-23.41851719-33.12863417-47.97940115-50.26413458-71.96910184-3.42710013-4.56946676-7.9965669-7.9965669-12.56603359-11.99485029-2.85591675 3.99828343-6.28301684 7.9965669-9.13893351 11.99485029-24.56088395 42.26756771-54.26241805 81.67921862-92.53170232 117.09258631-6.28301684-19.99141721-6.28301684-37.12691754 3.99828348-53.69123469 22.84733386-37.12691754 44.55230111-75.39620181 66.82845162-112.52311944 11.99485033-19.99141721-2.85591675-35.41336751-8.56775021-51.97768461-14.27958373-41.12520094-29.70153417-82.25040197-43.98111771-123.37560296-8.56775021-26.27443397-14.85076698-53.12005121-23.41851728-83.39276876 26.27443397 0 48.5505845-0.57118343 70.25555174 0.57118332 2.85591675 0 7.42538352 8.56775021 8.56775008 13.70840043 6.85420015 39.41165101 11.99485033 79.39448523 18.8490506 119.37731949 1.14236668 5.71183343 6.28301684 10.85248359 9.71011686 15.99313379 2.85591675-5.71183343 7.9965669-10.85248359 8.56775023-16.56431705 6.28301684-43.98111773 12.56603358-87.39105211 18.27786717-131.94335324h73.11146835c-17.70668378 78.82330201-35.98455091 155.36187039-70.25555172 227.33097223-9.13893356 18.84905043 4.56946676 33.12863417 11.9948503 47.40821777 15.42195035 29.70153417 32.55745077 58.8318847 52.54886802 86.24868552 14.85076698 18.27786711 17.70668378 38.26928428 9.71011682 63.9725349zM3596.84771427 238.3923779c5.14065014-0.57118343 10.2813003-1.14236668 15.4219504-1.14236658 85.67750212 0 171.35500425-0.57118343 257.03250623 0.57118328 20.56260047 0 29.70153417-5.14065014 26.27443395-26.27443395-1.14236668-7.9965669 0-15.99313367 0-26.84561727 26.84561727 0 51.9776846-0.57118343 77.10975196 0.57118329 2.85591675 0 7.42538352 6.85420015 7.9965669 10.85248367 1.14236668 6.85420015 0.57118343 13.70840029 0 20.56260049-1.7135501 15.99313367 5.14065014 21.70496722 21.13378378 21.13378377 79.39448523-0.57118343 158.78897057-0.57118343 238.75463923-0.57118328 30.27271734 0 41.12520094 12.56603358 37.69810086 45.12348433H3637.97291523c-32.55745077-1.7135501-38.84046764-8.56775021-41.12520096-43.98111775zM3479.18394471 260.6685285h-17.70668379c-206.19718841 0-412.39437671 0-619.1627484 0.57118329-21.13378386 0-31.4150841-6.28301684-37.69810095-27.41680066-5.14065014-18.27786711 1.7135501-18.27786711 15.42195047-18.27786703H3428.91981014c37.69810095 1.14236668 39.98283428 3.42710013 50.26413457 45.1234844zM4363.37576636 828.99595911c11.99485033-26.84561727 26.84561727-52.54886791 36.55573425-79.96566869 15.99313367-47.97940115 28.55916739-96.52998574 42.83875098-145.08057021 1.7135501-5.14065014 5.14065014-14.27958373 7.99656685-14.27958373 26.84561727-1.14236668 53.69123465-0.57118343 82.25040204-0.57118333-22.84733386 106.81128602-62.83016817 196.48707141-162.78725396 245.03765601-2.85591675-1.7135501-5.14065014-3.42710013-6.85420016-5.14065005zM5033.37383267 830.13832574c-63.40135157 2.28473334-166.78553742-149.65003708-165.07198737-242.18173924H4951.69461405c15.99313367 83.39276864 37.12691754 166.78553742 81.67921862 242.18173924z" p-id="1721"></path><path d="M626.69430832 409.74738204c7.42538352 42.26756771 16.56431707 82.25040197 21.13378383 123.37560309 6.85420015 61.11661813-54.26241805 118.23495283-101.67063569 134.22808662-74.8250185 25.70325061-161.0737039-35.41336751-180.4939379-106.81128589-22.27615056-83.39276864 23.98970064-185.06340457 101.0994526-218.19203864 8.56775021-3.99828343 17.13550042-7.42538352 25.7032506-11.42366702 17.70668378-7.9965669 9.13893356-21.13378386 6.2830168-33.12863412-7.9965669-33.12863417-12.56603358-66.25726829 4.56946678-97.10116903 25.70325061-46.26585106 92.53170228-82.82158541 144.50938681-60.54543479 22.27615056 9.71011687 45.12348447 18.84905043 65.68608504 31.41508405 18.84905043 11.42366688 26.27443397 37.12691754 19.99141706 53.69123469-10.85248359 28.55916739-39.41165101 35.98455091-69.68436838 18.27786711-10.85248359-6.28301684-21.70496722-13.70840029-33.12863408-18.84905053-22.27615056-10.2813003-46.26585106 4.56946676-45.12348441 29.1303507 0.57118343 19.42023388 4.56946676 38.84046764 7.42538346 56.54715142 27.41680065 7.9965669 53.69123465 12.56603358 77.68093523 22.84733391 73.68265177 32.55745077 126.23151979 85.67750212 150.79240371 165.07198738 45.6946678 147.36530365-39.98283428 294.15942384-166.78553736 350.13539193-71.39791843 31.98626742-145.65175349 42.26756771-221.61913879 25.1320672-47.4082179-10.85248359-90.81815222-31.4150841-130.80098652-61.68780145-48.5505845-37.12691754-85.10631875-81.67921862-109.09601933-137.08400335-34.27100084-77.68093526-43.40993431-157.07542049-16.56431707-241.03937257 35.98455091-112.5231194 107.95365264-189.061688 217.62085528-231.32925567 19.42023388-7.42538352 42.83875106 0.57118343 53.12005131 18.27786715 11.99485033 19.99141721 7.9965669 42.26756771-11.42366689 55.975968-14.85076698 10.2813003-31.98626742 17.13550042-47.97940123 25.70325063-60.54543483 30.84390082-98.24353568 81.10803537-122.23323631 143.93820341-46.83703452 122.23323628 17.70668378 263.88670651 124.51796968 319.29149121 98.24353568 51.40650127 232.47162227 30.84390082 305.01190742-53.69123465 45.12348447-52.54886791 58.2607014-114.80785275 44.55230121-179.92275432-11.42366688-53.69123465-47.97940115-91.96051893-96.5299858-118.23495295-2.28473334-1.14236668-5.14065014-1.7135501-7.99656677-2.28473333-3.99828343-1.7135501-7.42538352-1.7135501-8.56775029-1.71355019z m-93.10288551 0c-71.96910175 16.56431707-104.52655253 89.67578557-73.11146845 148.50767041 10.85248359 20.56260047 37.69810095 31.98626742 57.11833466 26.27443395 26.84561727-7.9965669 45.6946678-27.41680065 41.69638431-52.54886789-6.28301684-41.12520094-17.13550042-81.67921862-25.70325052-122.23323647z" fill="#FFFFFF" p-id="1722"></path><path d="M533.59142281 409.74738204c8.56775021 40.55401772 19.42023388 81.10803537 25.70325052 122.23323647 3.99828343 25.13206727-14.85076698 44.55230111-41.69638431 52.54886789-19.42023388 5.71183343-46.26585106-5.71183343-57.11833466-26.27443395-31.4150841-59.40306815 0.57118343-132.51453654 73.11146845-148.50767041z" fill="#E00000" p-id="1723"></path></svg>
                          </div> 
                          <a>{{_MY_NETEASE}}</a>
                          </div>
                        </li>
                        <li
                          ng-if="is_login('qq')"
                          ng-click="showTag(6, {platform:'qq', user: musicAuth.qq});"
                          ng-class="{ 'active':(current_tag==6 && tag_params.platform=='qq') && (window_url_stack.length ==0) }"
                        >
                          <div ng-class="{'opensidebar':isOpenSidebar}" class="sidebar-block">
                            <div>
                          <svg t="1659438737503"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1876" width="512" height="512"><path d="M2193.94246832 793.5246942c-29.08469133-9.08896607-58.16938267-18.1779321-80.34645979-24.7219877-61.80496903 6.18049689-120.33791038 15.99658023-178.87085163 16.36013891-91.98033632 0.72711727-172.32679609-68.71258326-181.7793208-160.32936094-6.90761419-63.98632093-8.3618488-130.5175523 2.18135187-193.4131973 6.54405559-38.17365737 33.447395-79.98290114 63.25920364-106.15912335 78.16510793-69.07614191 172.32679609-67.25834869 262.48933916-28.35757407 84.70916351 36.71942275 117.0658826 110.5218271 111.97606163 201.04792887-3.63558638 68.71258326 10.90675924 139.97007703-31.99316045 202.5021633-3.99914507 5.45337963 1.81779322 20.35928391 6.54405555 28.72113268 10.17964193 17.45081478 22.54063577 33.08383638 34.17451232 49.80753391-2.90846912 5.089821-5.45337963 9.81608331-7.6347315 14.54234569z m-75.25663884-141.78787027c17.81437341-77.43799067 21.44995987-153.42174675 5.81693834-229.76906141-1.09067596-5.81693827-4.72626233-11.63387652-7.9982902-17.08725619-41.44568518-71.25749377-161.78359548-98.16083323-239.94870339-53.07956164-42.53636106 24.35842898-67.62190732 60.71429314-68.71258327 111.24894426-0.72711727 36.35586418 1.09067596 72.71172832-1.09067588 109.06759249-3.99914507 62.895645 17.08725611 115.24808937 74.89308016 141.0607529 63.25920361 28.35757405 129.42687641 26.90333948 195.23099051-9.45252465-27.99401539-39.62789198-52.71600306-74.89308016-78.16510795-110.52182701 65.07699684-26.17622215 86.89051531 21.81351849 119.97435168 58.53294125zM1683.50613559 792.07045962c-29.81180863-8.72540737-59.98717587-17.81437341-102.88709558-30.53892596-57.44226538 31.99316048-135.24381463 36.71942275-212.68180536 12.72455252-77.43799067-24.35842898-122.51926218-81.07357708-130.51755227-163.2378301-3.27202778-35.9923055 0.3635587-72.71172832-2.18135183-108.70403378-11.63387652-160.69291958 109.79470977-253.76393182 277.39524353-224.67924054 101.06930237 17.45081478 163.23783004 86.52695668 165.41918182 189.05049367 1.81779322 77.43799067 16.72369751 158.51156768-29.08469125 231.22329599-4.36270369 6.90761419 1.81779322 23.26775306 7.27117279 33.08383636 9.45252465 17.45081478 22.17707715 33.08383638 33.81095363 49.44397527-2.18135187 3.63558638-4.36270369 7.63473149-6.54405548 11.63387657z m-82.52781166-140.69719431c6.54405559-0.72711727 12.72455248-1.81779322 19.268608-2.54491051 0-55.98803084 0.3635587-111.97606156 0-167.96409235-0.72711727-72.71172832-33.81095362-122.15570357-94.52524677-141.42431163-78.89222523-25.0855463-166.14629922-6.54405559-202.86572202 47.98974071-14.90590428 22.17707715-24.35842898 51.26176849-27.26689809 78.16510796-4.72626233 40.35500923-1.09067596 81.80069437-1.45423459 122.88282079-0.3635587 62.1685277 29.08469133 107.97691655 85.79983939 128.3362005 61.07785177 21.81351849 123.97349676 22.90419443 183.23355538-14.54234564-27.26689812-40.35500923-51.26176849-75.25663877-74.52952152-109.79470973 66.53123138-29.08469133 82.52781165 27.26689812 112.33962022 58.8964999zM3022.12905377 509.94895372v-121.79214487c42.89991974-9.08896607 60.35073451 4.72626233 56.35158945 45.08127153-2.18135187 23.26775306-0.3635587 47.26262341-0.36355861 75.62019745h165.05562324v48.35329933h-167.23697515v227.95126825c-96.70659865 16.36013886-113.06673753 10.17964193-116.70232387-42.53636108 18.5414907-1.45423455 36.71942275-3.27202778 59.26005852-5.08982096v-179.96152759c-49.08041659 0-97.43371596-1.45423455-145.78701524 0.36355866-35.9923055 1.45423455-53.07956163-9.81608331-48.71685799-49.08041661 7.27117279-67.62190732 11.99743516-135.24381463 18.54149075-210.13689479h378.46454588v42.89991966h-318.47737004c-4.72626233 52.35244436-10.17964193 97.07015727-11.9974351 142.15142885-0.3635587 8.3618488 14.90590428 23.99487037 23.99487029 24.72198765 34.17451231 3.27202778 68.71258326 1.45423455 107.61335787 1.45423452zM2312.09902683 785.16284541v-246.85631761c122.15570357 0 241.76649664-0.72711727 361.37728969 1.45423458 10.90675924 0.3635587 30.53892588 19.99572529 30.90248457 31.26604314 2.90846912 70.53037644 1.45423455 141.06075292 1.45423454 214.13603989H2312.09902683z m58.53294127-147.9683671h275.94100892c-1.45423455-18.1779321-2.18135187-32.72027775-3.63558633-48.71685803h-272.30542259v48.71685803z m-2.18135187 53.80667891c1.45423455 18.1779321 2.18135187 31.62960183 3.27202779 45.80838884h274.12321577c-0.72711727-17.08725611-1.45423455-31.26604318-2.54491051-45.80838884H2368.45061623zM2374.63111322 456.14227477c-10.17964193-27.6304567-16.72369751-45.08127157-23.99487032-65.0769968 32.72027775-8.72540737 59.26005857-10.17964193 69.43970048 29.81180863 6.90761419 27.26689812 24.72198764 36.71942275 52.71600303 34.53807091 25.0855463-2.18135187 51.26176849 2.90846912 75.62019742-1.81779322 16.72369751-3.27202778 39.26433326-14.90590428 44.3541543-28.72113271 13.81522839-35.26518823 34.53807098-45.80838883 72.3481696-33.08383632-6.54405559 18.5414907-13.08811108 36.71942275-21.81351845 61.44141039h111.24894429c1.81779322 18.90504942 2.90846912 31.99316048 4.36270373 48.71685795H2259.38302378V456.50583343c36.71942275-0.3635587 72.34816969-0.3635587 115.24808944-0.36355866zM2286.28636329 365.61617306v-50.5346512H2470.2470359v-46.17194746c25.44910489 7.27117279 60.35073451 8.3618488 64.34987956 19.99572529 10.90675924 30.90248452 31.26604318 25.81266358 52.35244437 26.17622217 45.4448302 0.3635587 91.25321901 0 138.51584242 0 6.54405559 26.90333948 17.08725611 50.53465115-23.63131166 50.5346512H2286.28636329z" p-id="1877"></path><path d="M2800.7218411 773.89252751c23.63131168-54.89735487 40.71856786-95.25236414 57.80582397-135.24381463 18.5414907-42.89991974 18.5414907-42.89991974 77.80154928-33.81095364-21.81351849 50.89820983-40.35500923 102.15997829-66.1676727 149.05904299-7.27117279 12.36099385-37.81009875 11.63387652-69.43970055 19.99572528zM3141.37628821 604.83775924c40.35500923-10.90675924 65.80411413-3.63558638 79.25578387 36.71942273 13.81522839 41.44568518 33.08383638 80.71001841 49.80753389 120.70146905-46.5355061 16.36013886-69.4397005-1.81779322-82.89137024-43.62703701-11.99743516-38.53721602-29.81180863-74.89308016-46.17194752-113.79385477z" p-id="1878"></path><path d="M755.34092368 39.86763025s-0.72711727 0.3635587-1.45423459 1.09067588c0.72711727-0.3635587 1.45423455-1.09067596 1.45423459-1.09067588z" fill="#FFDC00" p-id="1879"></path><path d="M770.24682802 130.7572906l-14.90590434-90.88966035s-9.81608331 91.98033632-118.15655847 190.50472812c-49.80753385 45.4448302-137.78872515 95.25236414-137.78872518 95.25236413l206.864867 358.10526193s19.63216668 59.26005857-6.54405552 114.88453071c-30.53892588 65.07699684-91.98033632 105.0684474-210.13689483 108.3404752-105.0684474 2.90846912-177.41661707-59.26005857-190.50472811-144.33278068-13.08811108-84.34560482 41.80924379-149.42260172 105.06844738-174.14458938 59.26005857-22.90419443 157.78445038-13.08811108 157.78445046-13.08811107l-229.76906152-387.55351188 3.27202777-39.26433328s73.80240421 9.45252465 203.59283929-16.3601389c59.26005857-11.99743516 109.06759251-31.62960183 146.15057394-49.80753387-56.71514804-24.72198764-119.61079312-38.53721602-185.41490716-38.53721603-255.58172505 0-463.17370937 207.2284257-463.1737094 463.17370936 0 255.58172505 207.2284257 463.17370937 463.1737094 463.17370937 255.58172505 0 463.17370937-207.2284257 463.17370928-463.17370937-0.72711727-155.23953995-76.3473147-291.93758914-192.68607999-376.28319401z" fill="#FFDC00" p-id="1880"></path><path d="M755.34092368 39.86763025s-85.4362808 65.80411413-216.68095035 91.98033629c-129.79043508 25.81266358-203.59283923 16.36013886-203.59283925 16.36013886l-3.27202778 39.2643333L561.56416771 575.38950921s-98.52439189-9.81608331-157.78445044 13.08811107c-63.25920361 24.35842898-118.15655847 89.79898447-105.06844736 174.14458938 13.08811108 85.4362808 85.4362808 147.24124981 190.50472819 144.33278068 118.15655847-3.27202778 179.59796889-43.2634783 210.13689478-108.3404752 26.17622215-55.98803084 6.54405559-114.88453074 6.54405555-114.88453071L499.03208139 325.6247225s87.98119123-49.80753385 137.78872514-95.25236413c108.70403381-98.52439189 118.52011714-190.5047282 118.52011715-190.50472812z" fill="#0AC094" p-id="1881"></path></svg>
                          </div>
                          <a>{{_MY_QQ}}</a>
                          </div>
                        </li>
                            </ul>
                            <div class="menu-title created-title" ng-init="loadMyPlaylist();">
                        <div ng-class="{'opensidebar':isOpenSidebar}" class="title">{{_CREATED_PLAYLIST}}</div>
                        <svg ng-click="showDialog(5)" t="1659442568393"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1663" width="512" height="512"><path d="M842 62H182c-66 0-120 54-120 120v660c0 66 54 120 120 120h660c66 0 120-54 120-120V182c0-66-54-120-120-120z m30 750c0 33-27 60-60 60H212c-33 0-60-27-60-60V212c0-33 27-60 60-60h600c33 0 60 27 60 60v600z" fill="currentColor" p-id="1664"></path><path d="M737 467H557V287c0-24.8-20.2-45-45-45s-45 20.2-45 45v180H287c-24.8 0-45 20.2-45 45s20.2 45 45 45h180v180c0 24.8 20.2 45 45 45s45-20.2 45-45V557h180c24.8 0 45-20.2 45-45s-20.2-45-45-45z" fill="currentColor" p-id="1665"></path></svg>
                            </div>
                            <ul class="nav masthead-nav">
                        <li
                          ng-repeat="i in myplaylists track by $index"
                          ng-class="{ 'active':window_type=='list' && ( ('/playlist?list_id='+i.info.id) === getCurrentUrl() ) }"
                          ng-click="showPlaylist(i.info.id)"
                          drag-drop-zone
                          drag-zone-type="'application/listen1-myplaylist'"
                          drop-zone-ondrop="onSidebarPlaylistDrop('my', i.info.id, arg1, arg2, arg3)"
                          draggable="true"
                          sortable="true"
                          drag-zone-object="i"
                          drag-zone-title="i.info.title"
                        >
                          <div ng-class="{'opensidebar':isOpenSidebar}" class="sidebar-block">
                            
                            <a>{{i.info.title}}</a>
                          </div>
                        </li>
                            </ul>
                            <div class="menu-title" ng-init="loadFavoritePlaylist();">
                        <div ng-class="{'opensidebar':isOpenSidebar}" class="title">{{_FAVORITED_PLAYLIST}}</div>
                            </div>
                            <ul class="nav masthead-nav">
                        <li
                          ng-repeat="i in favoriteplaylists track by $index"
                          ng-class="{ 'active':window_type=='list' && ( ('/playlist?list_id='+i.info.id) === getCurrentUrl() ) }"
                          ng-click="showPlaylist(i.info.id, {useCache: false})"
                          drag-drop-zone
                          drag-zone-type="'application/listen1-favoriteplaylist'"
                          drop-zone-ondrop="onSidebarPlaylistDrop('favorite', i.info.id, arg1, arg2, arg3)"
                          draggable="true"
                          sortable="true"
                          drag-zone-object="i"
                          drag-zone-title="i.info.title"
                        >
                          <div ng-class="{'opensidebar':isOpenSidebar}" class="sidebar-block">
                            <a>{{i.info.title}}</a>
                          </div>
                        </li>
                            </ul>
                          </div>
                        </div>
                    </div>
                  </div>

                  <div class="content" ng-controller="InstantSearchController">
                    <div class="navigation">
                      <div class="backfront">
                        <svg  fill="currentColor" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="angle-left" class="svg-inline--fa fa-angle-left fa-w-8 icon li-back" ng-click="popWindow()" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512"><path fill="currentColor" d="M31.7 239l136-136c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9L127.9 256l96.4 96.4c9.4 9.4 9.4 24.6 0 33.9L201.7 409c-9.4 9.4-24.6 9.4-33.9 0l-136-136c-9.5-9.4-9.5-24.6-.1-34z"></path></svg>
                        <!-- <span class="icon li-back" ng-click="popWindow()"></span> -->
                        <svg fill="currentColor" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="angle-right" class="svg-inline--fa fa-angle-right fa-w-8 icon li-advance" ng-click="forwardWindow()" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512"><path fill="currentColor" d="M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34z"></path></svg>
                        <!-- <span
                          class="icon li-advance"
                          ng-click="forwardWindow()"
                        ></span> -->
                      </div>
                      <div class="search">
                        <svg fill="currentColor" style="opacity: 0.28;margin-right: 4px;width: 15px;height: 15px;cursor: default;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="search" class="svg-inline--fa fa-search fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"></path></svg>
                        <input
                          class="form-control search-input"
                          id="search-input"
                          type="text"
                          ng-model="keywords"
                          placeholder="{{_SEARCH_PLACEHOLDER}}"
                          ng-model-options="{debounce: 500}"
                          ng-keyup="enterEvent($event)"
                        />
                      </div>
                      <div
                        ng-class="{ 'active': (current_tag==4) && (window_url_stack.length ==0)}"
                        ng-click="showTag(5)"
                        class="settings"
                      >
                        <span class="icon">
                          <svg t="1660120656679" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2962" width="512" height="512"><path d="M617.386667 896a42.666667 42.666667 0 0 1-42.666667-42.666667v-184.746666a59.306667 59.306667 0 0 0-59.306667-59.306667H209.92a59.306667 59.306667 0 0 0-59.306667 59.306667V853.333333a42.666667 42.666667 0 0 1-85.333333 0v-184.746666a144.64 144.64 0 0 1 144.64-144.64h305.493333a144.64 144.64 0 0 1 144.64 144.64V853.333333a42.666667 42.666667 0 0 1-42.666666 42.666667zM922.026667 896a42.666667 42.666667 0 0 1-42.666667-42.666667v-184.746666a59.306667 59.306667 0 0 0-59.306667-59.306667h-128a42.666667 42.666667 0 1 1 0-85.333333h128a144.64 144.64 0 0 1 144.64 144.64V853.333333a42.666667 42.666667 0 0 1-42.666666 42.666667zM362.666667 491.093333a195.84 195.84 0 1 1 195.413333-195.413333 195.84 195.84 0 0 1-195.413333 195.413333z m0-305.92a110.506667 110.506667 0 1 0 110.08 110.506667A110.506667 110.506667 0 0 0 362.666667 185.173333zM659.2 491.093333a42.666667 42.666667 0 0 1 0-85.333333 110.506667 110.506667 0 0 0 0-220.586667 42.666667 42.666667 0 0 1 0-85.333333 195.84 195.84 0 0 1 0 391.253333z" fill="currentColor" p-id="2963"></path></svg>
                        </span>
                      </div>
                      <div
                        ng-class="{ 'active': (current_tag==4) && (window_url_stack.length ==0)}"
                        ng-click="showTag(4)"
                        class="settings is-setting"
                      >
                      <svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="cog" fill="currentColor" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M487.4 315.7l-42.6-24.6c4.3-23.2 4.3-47 0-70.2l42.6-24.6c4.9-2.8 7.1-8.6 5.5-14-11.1-35.6-30-67.8-54.7-94.6-3.8-4.1-10-5.1-14.8-2.3L380.8 110c-17.9-15.4-38.5-27.3-60.8-35.1V25.8c0-5.6-3.9-10.5-9.4-11.7-36.7-8.2-74.3-7.8-109.2 0-5.5 1.2-9.4 6.1-9.4 11.7V75c-22.2 7.9-42.8 19.8-60.8 35.1L88.7 85.5c-4.9-2.8-11-1.9-14.8 2.3-24.7 26.7-43.6 58.9-54.7 94.6-1.7 5.4.6 11.2 5.5 14L67.3 221c-4.3 23.2-4.3 47 0 70.2l-42.6 24.6c-4.9 2.8-7.1 8.6-5.5 14 11.1 35.6 30 67.8 54.7 94.6 3.8 4.1 10 5.1 14.8 2.3l42.6-24.6c17.9 15.4 38.5 27.3 60.8 35.1v49.2c0 5.6 3.9 10.5 9.4 11.7 36.7 8.2 74.3 7.8 109.2 0 5.5-1.2 9.4-6.1 9.4-11.7v-49.2c22.2-7.9 42.8-19.8 60.8-35.1l42.6 24.6c4.9 2.8 11 1.9 14.8-2.3 24.7-26.7 43.6-58.9 54.7-94.6 1.5-5.5-.7-11.3-5.6-14.1zM256 336c-44.1 0-80-35.9-80-80s35.9-80 80-80 80 35.9 80 80-35.9 80-80 80z"></path></svg>
                        <!-- <span class="icon li-setting"></span> -->
                      </div>
                      <div ng-if="!isChrome && !isMac" class="window-control">
                        <svg class="icon" window-control="window_min">
                          <use href="#minimize-2"></use>
                        </svg>
                        <svg class="icon" window-control="window_max">
                          <use href="#maximize"></use>
                        </svg>
                        <svg class="icon" window-control="window_close">
                          <use href="#x"></use>
                        </svg>
                      </div>
                    </div>
                    <div
                    style="overflow-y: scroll;"
                      class="browser flex-scroll-wrapper"
                      infinite-scroll="scrolling()"
                      content-selector="'#playlist-content'"
                    >
                      <div style="height: 64px;"></div>
                      <!-- hot playlist window-->
                      <div
                        class="page page-hot-playlist"
                        ng-show="current_tag==2 && is_window_hidden==1"
                        ng-controller="PlayListController"
                        ng-init="loadPlaylist();"
                      >
                        <div class="source-list" ng-show="is_window_hidden==1">
                          <div
                            ng-repeat-start="source in ::sourceList"
                            class="source-button"
                            ng-class="{'active':tab === source.name}"
                            ng-click="changeTab(source.name)"
                          >
                          <div class="buttontext">
                            {{source.displayText}}
                          </div>
                          </div>
                          <div
                            ng-repeat-end
                            ng-if="!$last"
                            class="splitter"
                          ></div>
                        </div>
                        <div class="playlist-filter">
                          <div
                            class="l1-button filter-item"
                            ng-repeat="filter in playlistFilters[tab] || []"
                            ng-click="changeFilter(filter.id)"
                            ng-class="{'active':filter.id === currentFilterId}"
                          >
                            {{filter.name}}
                          </div>
                          <div
                            class="l1-button filter-item"
                            ng-show="playlistFilters[tab] && playlistFilters[tab].length > 0"
                            ng-click="toggleMorePlaylists()"
                            ng-class="{'active':showMore}"
                          >
                          <svg fill="currentColor" aria-hidden="true" focusable="false" data-prefix="far" data-icon="ellipsis-h" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="svg-inline--fa fa-ellipsis-h fa-w-16 fa-9x"><path fill="currentColor" d="M304 256c0 26.5-21.5 48-48 48s-48-21.5-48-48 21.5-48 48-48 48 21.5 48 48zm120-48c-26.5 0-48 21.5-48 48s21.5 48 48 48 48-21.5 48-48-21.5-48-48-48zm-336 0c-26.5 0-48 21.5-48 48s21.5 48 48 48 48-21.5 48-48-21.5-48-48-48z" class=""></path></svg>
                          </div>
                        </div>
                        <div class="all-playlist-filter" ng-show="showMore">
                          <div
                            ng-repeat="category in allPlaylistFilters[tab] || []"
                            class="category"
                          >
                            <div class="category-title">
                              {{category.category}}
                            </div>
                            <div class="category-filters">
                              <div
                                class="filter-item"
                                ng-repeat="filter in category.filters"
                              >
                                <span ng-click="changeFilter(filter.id)" ng-class="{'active':filter.id === currentFilterId}">
                                  {{filter.name}}</span
                                >
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="site-wrapper-innerd" id="hotplaylist">
                          <div class="cover-container" id="playlist-content">
                            <ul  ng-style="{'padding-bottom':playlist.length == 0?'0px':'120px'}" class="playlist-covers">
                              <li ng-repeat="i in result ">
                                <div class="u-cover">
                                  <img
                                  err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png"
                                    ng-src="{{i.cover_img_url}}"
                                    ng-click="showPlaylist(i.id)"
                                  />
                                  <div
                                      class="covershadow"
                                      style="background-image:url({{i.cover_img_url}});"
                                    ></div>
                                  <div
                                    class="bottom"
                                    ng-click="directplaylist(i.id)"
                                  >  
                                  <svg  fill="currentColor" style="height: 44%;width:44%;margin-left: 4px;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="play" class="svg-inline--fa fa-play fa-w-14" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z"></path></svg>
                                  </div>
                                </div>
                                <div class="desc">
                                  <span
                                    class="title"
                                    ng-click="showPlaylist(i.id)"
                                    ><a href="">{{i.title}}</a></span
                                  >
                                </div>
                              </li>
                              <!-- <div class="loading_bottom">
                                <img src="images/loading-1.gif" height="40px" />
                              </div> -->
                            </ul>
                          </div>
                        </div>
                      </div>
                      <!-- my platform window-->
                      <div
                        class="page page-hot-playlist"
                        ng-show="current_tag==6 && is_window_hidden==1"
                        ng-controller="PlatformController"
                      >
                        <div class="source-list" ng-show="is_window_hidden==1">
                          <div
                            ng-repeat-start="source in ::platformSourceList"
                            class="source-button"
                            ng-class="{'active':tab === source.name}"
                            ng-click="changeTab(source.name)"
                          >
                          <div class="buttontext">
                            {{source.displayText}}
                          </div>
                          </div>
                          <div
                            ng-repeat-end
                            ng-if="!$last"
                            class="splitter"
                          ></div>
                        </div>
                        <div class="site-wrapper-innerd" id="hotplaylist">
                          <div class="cover-container" id="playlist-content">
                            <ul ng-style="{'padding-bottom':playlist.length == 0?'0px':'140px'}"  class="playlist-covers">
                              <li ng-repeat="i in myPlatformPlaylists">
                                <div class="u-cover">
                                  <img
                                  err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png"
                                    ng-src="{{i.cover_img_url}}"
                                    ng-click="showPlaylist(i.id)"
                                  />
                                  <div
                                      class="covershadow"
                                      style="background-image:url({{i.cover_img_url}});"
                                    ></div>
                                  <div
                                    class="bottom"
                                    ng-click="directplaylist(i.id)"
                                  >
                                  <svg style="height: 44%;width:44%;margin-left: 4px;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="play" class="svg-inline--fa fa-play fa-w-14" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z"></path></svg>
                                  </div>
                                </div>
                                <div class="desc">
                                  <span
                                    class="title"
                                    ng-click="showPlaylist(i.id)"
                                    >{{i.title}}</span
                                  >
                                </div>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <!-- content page: 快速搜索 -->
                      <div
                        class="page"
                        ng-show="current_tag==3 && is_window_hidden==1"
                      >
                        <div class="site-wrapper-innerd">
                          <div class="cover-container">
                            <!-- Initialize a new AngularJS app and associate it with a module named "instantSearch"-->
                            <div ng-class="{footerdef:playlist.length == 0}" class="searchbox">
                              <ul class="source-list">
                                <li
                                  class="source-button"
                                  ng-class="{'active':tab === 'allmusic'}"
                                  ng-click="changeSourceTab('allmusic')"
                                >
                                <div class="buttontext">
                                  <a>{{_ALL_MUSIC}}(Beta)</a>
                                </div>
                                </li>
                                <div class="splitter"></div>
                                <div
                                  ng-repeat-start="source in ::sourceList"
                                  class="source-button"
                                  ng-class="{'active':tab === source.name}"
                                  ng-click="changeSourceTab(source.name)"
                                >
                                <div class="buttontext">
                                  {{source.displayText}}
                                </div>
                                </div>
                                <div
                                  ng-repeat-end
                                  ng-if="!$last"
                                  class="splitter"
                                ></div>
                                <svg
                                  fill="currentColor"
                                  class="searchspinner"
                                  ng-show="loading"
                                  version="1.1"
                                  id="loader-1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  xmlns:xlink="http://www.w3.org/1999/xlink"
                                  x="0px"
                                  y="0px"
                                  width="40px"
                                  height="40px"
                                  viewBox="0 0 40 40"
                                  enable-background="new 0 0 40 40"
                                  xml:space="preserve"
                                >
                                  <path
                                    opacity="0.2"
                                    fill="currentColor"
                                    d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946 s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634 c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"
                                  />
                                  <path
                                  fill="currentColor"
                                    d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0 C22.32,8.481,24.301,9.057,26.013,10.047z"
                                  >
                                    <animateTransform
                                      attributeType="xml"
                                      attributeName="transform"
                                      type="rotate"
                                      from="0 20 20"
                                      to="360 20 20"
                                      dur="0.6s"
                                      repeatCount="indefinite"
                                    />
                                  </path>
                                </svg>
                                <div class="search-type">
                                  <li
                                    class="source-button"
                                    ng-class="{'active':isSearchType(0)}"
                                    ng-click="changeSearchType(0)"
                                  >
                                  <div class="buttontext">
                                    <a>单曲</a>
                                  </div>
                                  </li>
                                  <div class="splitter"></div>
                                  <li
                                    class="source-button"
                                    ng-class="{'active':isSearchType(1)}"
                                    ng-click="changeSearchType(1)"
                                  >
                                  <div class="buttontext">
                                    <a>歌单</a>
                                  </div>
                                  </li>
                                </div>
                              </ul>
                              <h1 style="
                              margin-top: 32px;
                              margin-bottom: 28px;
                              margin-left: 26px;
                              margin-right: 26px;
                          "><span style="opacity: .58;user-select: none;">搜索 <span ng-show="isSearchType(0)">歌曲</span><span ng-show="isSearchType(1)">歌单</span> </span>"{{keywords}}"</h1>
                              <ul ng-class="{'isSearch':isSearchType(1),isSearchOne:isSearchType(0)}" class="detail-songlist">
                                <!-- <li class="head" ng-if="searchType===0 ">
                                  <div class="title"><a>{{_SONGS}}</a></div>
                                  <div class="artist"><a>{{_ARTISTS}}</a></div>
                                  <div class="album"><a>{{_ALBUMS}}</a></div>
                                  <div class="tools">{{_OPERATION}}</div>
                                </li> -->
                                <!-- <li class="head" ng-if="searchType===1 ">
                                  <div class="title">
                                    <a>{{_PLAYLIST_TITLE}}</a>
                                  </div>
                                  <div class="artist">
                                    <a>{{_PLAYLIST_AUTHOR}}</a>
                                  </div>
                                  <div class="album">
                                    <a>{{_PLAYLIST_SONG_COUNT}}</a>
                                  </div>
                                </li> -->
                                <li
                                  ng-class="{ 'isSearchType':isSearchType(0),'playing': currentPlaying.id == song.id }"
                                  ng-if="searchType===0"
                                  ng-repeat="song in result"
                                  ng-mouseenter="options=true"
                                  ng-mouseleave="options=false"
                                  ng-dblclick="addAndPlay(song)"
                                >
                                <img
                                add-and-play="song"
                                        ng-src="{{ song.img_url }}?param=224y224"
                                        err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png"
                                      />
                                      <div class="title-and-artist">
                                        <div class="container">
                                  <div class="title">
                                    <!-- <a ng-if="song.disabled" class="disabled" ng-click="copyrightNotice()">{{ song.title |limitTo:30}}</a> -->
                                    <a add-and-play="song"
                                      ><span
                                        ng-if="isActiveTab('allmusic')"
                                        class="source"
                                        >{{song.sourceName}}</span
                                      >{{ song.title |limitTo:30}}</a
                                    >
                                  </div>
                                  <div class="artist">
                                    <a ng-click="showPlaylist(song.artist_id)"
                                      >{{ song.artist |limitTo:20}}</a
                                    >
                                  </div>
                                </div>
                              </div>
                                  <div class="album">
                                    <a ng-click="showPlaylist(song.album_id)"
                                      >{{ song.album |limitTo:30}}</a
                                    >
                                  </div>

                                  <div class="tools">
                                    <a
                                      title="{{_ADD_TO_QUEUE}}"
                                      class="detail-add-button"
                                      add-without-play="song"
                                      ng-show="options"
                                      >
                                      <svg t="1659670196701" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2178" width="512" height="512"><path d="M938.516167 597.182834 85.483833 597.182834C38.527925 597.182834 0 559.256908 0 511.699001c0-46.955908 37.925926-85.483833 85.483833-85.483833l853.032334 0c46.955908 0 85.483833 37.925926 85.483833 85.483833C1024 559.256908 986.074074 597.182834 938.516167 597.182834L938.516167 597.182834 938.516167 597.182834zM512.300999 1024c-46.955908 0-85.483833-37.925926-85.483833-85.483833L426.817166 85.483833C426.817166 37.925926 464.743092 0 512.300999 0c46.955908 0 85.483833 37.925926 85.483833 85.483833l0 853.634333C597.182834 985.472075 559.256908 1024 512.300999 1024L512.300999 1024 512.300999 1024zM512.300999 1024" fill="currentColor" p-id="2179"></path></svg>
                                      </a>
                                    <a
                                      title="{{_ADD_TO_PLAYLIST}}"
                                      class="detail-fav-button"
                                      ng-show="options"
                                      ng-click="showDialog(0, song)"
                                      >
                                      <svg t="1659670240206" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2332" width="512" height="512"><path d="M768 768v192h-128v-192h-192v-128h192v-192h128v192h192v128h-192z m64-576H192v640h192v128H64V64h896v320h-128V192z" fill="currentColor" p-id="2333"></path></svg></a>
                                    <a
                                      title="{{_REMOVE_FROM_PLAYLIST}}"
                                      class="detail-delete-button"
                                      ng-click="removeSongFromPlaylist(song, list_id)"
                                      ng-show="options && is_mine=='1' "
                                      >
                                      <svg t="1659670296999" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2486" width="512" height="512"><path d="M895.464448 119.006208 677.967872 119.006208c0 0-32.8448 1.020928-58.648576-26.943488-10.395648-12.050432-27.804672-23.795712-56.4224-24.799232l-41.183232 0-6.280192 0-41.182208 0c-28.618752 1.004544-46.031872 12.749824-56.4224 24.799232-25.807872 27.964416-58.6496 26.943488-58.6496 26.943488L141.682688 119.006208c-13.99296 0-25.33888 11.34592-25.33888 25.33888l0 93.090816c-0.053248 26.927104 26.083328 26.396672 26.083328 26.396672l49.83808 0L192.265216 913.65376c0 0-3.966976 44.084224 40.121344 46.45888l269.31712 0 33.738752 0 30.808064 0.238592 38.500352 0 174.934016 0 24.297472 0 0.782336-0.238592c44.080128-2.374656 40.117248-46.45888 40.117248-46.45888L844.88192 263.832576l49.842176 0c0 0 26.133504 0.530432 26.083328-26.396672l0-93.090816C920.8064 130.353152 909.46048 119.006208 895.464448 119.006208zM430.539776 803.171328c0 17.042432-13.828096 30.865408-30.865408 30.865408-17.042432 0-30.865408-13.824-30.865408-30.865408L368.80896 320.736256c0-17.042432 13.824-30.865408 30.865408-30.865408 17.038336 0 30.865408 13.824 30.865408 30.865408L430.539776 803.171328zM663.436288 803.171328c0 17.042432-13.824 30.865408-30.865408 30.865408-17.038336 0-30.865408-13.824-30.865408-30.865408L601.705472 320.736256c0-17.042432 13.828096-30.865408 30.865408-30.865408 17.041408 0 30.865408 13.824 30.865408 30.865408L663.436288 803.171328z" fill="currentColor" p-id="2487"></path></svg>
                                    </a>
                                    <a
                                      title="{{_ORIGIN_LINK}}"
                                      class="source-button"
                                      open-url="song.source_url"
                                      ng-show="options"
                                      >
                                      <svg t="1659670349346" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2640" width="512" height="512"><path d="M475.66342293 318.38570027A54.23369707 54.23369707 0 0 1 399.7362464 241.91618667L512 128.56775893a271.16848747 271.16848747 0 0 1 383.43224107 383.43224107L779.372128 625.8907648a54.23369707 54.23369707 0 1 1-76.46951253-76.4695136l113.89076373-113.8907648a162.70109227 162.70109227 0 0 0-229.9508768-229.9508768z m74.8425024 385.60158826A54.23369707 54.23369707 0 1 1 626.97543893 779.372128l-114.43310186 114.43310187A271.16848747 271.16848747 0 0 1 128.56775893 512L243.0008608 399.7362464a54.23369707 54.23369707 0 1 1 77.01185067 74.30016533l-114.97543894 114.43310187a162.70109227 162.70109227 0 0 0 229.9508768 229.9508768z m-135.0419072-17.89712a54.23369707 54.23369707 0 1 1-76.46951253-76.4695136l268.45680213-268.45680213a54.23369707 54.23369707 0 0 1 76.4695136 76.4695136z" fill="currentColor" p-id="2641"></path></svg>
                                      </a>
                                    <!-- <a
                                      title="{{_ORIGIN_LINK}}"
                                      class="source-button"
                                      ng-click="download_music(song.artist,song.title,song.url)"
                                      ng-show="options" 
                                    >
                                      <svg t="1659785735308" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2358" width="512" height="512"><path d="M828.975746 894.125047 190.189132 894.125047c-70.550823 0-127.753639-57.18542-127.753639-127.752616L62.435493 606.674243c0-17.634636 14.308891-31.933293 31.93227-31.933293l63.889099 0c17.634636 0 31.93227 14.298658 31.93227 31.933293l0 95.821369c0 35.282574 28.596292 63.877843 63.87682 63.877843L765.098927 766.373455c35.281551 0 63.87682-28.595268 63.87682-63.877843l0-95.821369c0-17.634636 14.298658-31.933293 31.943526-31.933293l63.877843 0c17.634636 0 31.933293 14.298658 31.933293 31.933293l0 159.699212C956.729385 836.939627 899.538849 894.125047 828.975746 894.125047L828.975746 894.125047zM249.938957 267.509636c12.921287-12.919241 33.884738-12.919241 46.807049 0l148.97087 148.971893L445.716876 94.89323c0-17.634636 14.300704-31.94762 31.933293-31.94762l63.875796 0c17.637706 0 31.945573 14.312984 31.945573 31.94762l0 321.588299 148.97087-148.971893c12.921287-12.919241 33.875528-12.919241 46.796816 0l46.814212 46.818305c12.921287 12.922311 12.921287 33.874505 0 46.807049L552.261471 624.930025c-1.140986 1.137916-21.664416 13.68365-42.315758 13.69286-20.87647 0.010233-41.878806-12.541641-43.020816-13.69286L203.121676 361.13499c-12.922311-12.933567-12.922311-33.884738 0-46.807049L249.938957 267.509636 249.938957 267.509636z" fill="currentColor" p-id="2359"></path></svg>
                                    </a> -->
                                  </div>
                                </li>
                                <li
                                  ng-if="searchType===1"
                                  ng-repeat="playlist in result"
                                  ng-class-odd="'odd'"
                                  ng-class-even="'even'"
                                  class="playlist-result isSearchGeDan"
                                >
                                <!-- <a ng-click="showPlaylist(playlist.id)"> -->
                                  <div class="u-cover">
                                  <img
                                      ng-click="showPlaylist(playlist.id)"
                                      ng-src="{{ playlist.img_url }}?param=512y512"
                                      err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png"
                                      />
                                      <div class="covershadow" style="background-image:url({{playlist.img_url}}?param=512y512);"></div>
                                      <div 
                                      ng-click="directplaylist(playlist.id)"
                                      class="bottom" style="height:50px;width:50px">
                                        <svg fill="currentColor" style="height: 44%;width:44%;margin-left: 4px;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="play" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z"></path></svg>
                                      </div>
                                    </div>
                                  <div class="desc">
                                      <div
                                      ng-click="showPlaylist(playlist.id)"
                                      class="title">
                                        <a href="">{{ playlist.title |limitTo:30}}</a>  <span
                                          ng-if="isActiveTab('allmusic')"
                                          class="source playlist"
                                          >  {{playlist.sourceName}}</span
                                        >
                                      </div>
                                      <div class="artist">
                                        {{ playlist.author |limitTo:20}}
                                      </div>
                                  </div>
                                  
                                  <!-- <div class="album">
                                    {{ playlist.count |limitTo:30}}
                                  </div> -->
                                <!-- </a> -->
                                </li>
                              </ul>
                              <div
                                class="search-pagination"
                                ng-show="totalpage>1"
                                pagination
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- content page: 设置 -->
                      <div
                        class="page"
                        ng-show="current_tag==4 && is_window_hidden==1"
                        ng-init="lastfm.updateStatus(); updateGithubStatus();"
                      >
                        <div class="site-wrapper-innerd">
                          <div class="cover-container">
                            <div class="settings-title">
                              <span>{{_LANGUAGE}}</span>
                            </div>
                            <div class="settings-content">
                              <div>
                                <button
                                  class="language-button"
                                  ng-click="setLang('zh-CN')"
                                >
                                  简体中文
                                </button>
                                <button
                                  class="language-button"
                                  ng-click="setLang('zh-TC')"
                                >
                                  繁体中文
                                </button>
                                <button
                                  class="language-button"
                                  ng-click="setLang('en-US')"
                                >
                                  English
                                </button>
                                <button
                                  class="language-button"
                                  ng-click="setLang('fr-FR')"
                                >
                                  French
                                </button>
                                <button
                                  class="language-button"
                                  ng-click="setLang('ko-KR')"
                                >
                                  Korean
                                </button>
                              </div>
                            </div>
                            <div class="settings-title">
                              <span>{{_THEME}}</span>
                            </div>
                            <div class="settings-content">
                              <div>
                                <button
                                  class="theme-button"
                                  ng-click="setTheme('white')"
                                >
                                  {{_THEME_WHITE}}
                                </button>
                                <button
                                  class="theme-button"
                                  ng-click="setTheme('black')"
                                >
                                  {{_THEME_BLACK}}
                                </button>
                                <button
                                class="theme-button"
                                ng-click="setTheme('white2')"
                              >
                                {{_THEME_MODERN_WHITE}}
                              </button>
                              <button
                                class="theme-button"
                                ng-click="setTheme('black2')"
                              >
                                {{_THEME_MODERN_BLACK}}
                              </button>
                              </div>
                            </div>
                            <div class="settings-title">
                              <span>{{_AUTO_CHOOSE_SOURCE}}</span>
                            </div>
                            <div class="settings-content">
                              <div
                                class="shortcut"
                                class="btn btn-primary confirm-button"
                              >
                                <svg
                                  class="feather"
                                  ng-show="!enableAutoChooseSource"
                                  ng-click="setAutoChooseSource(true)"
                                >
                                  <use href="#square"></use>
                                </svg>
                                <svg
                                  class="feather"
                                  ng-show="enableAutoChooseSource"
                                  ng-click="setAutoChooseSource(true)"
                                >
                                  <use href="#check-square"></use>
                                </svg>
                                {{_AUTO_CHOOSE_SOURCE_NOTICE}}
                              </div>
                              <div
                                class="search-description"
                                ng-show="enableAutoChooseSource"
                              >
                                {{_AUTO_CHOOSE_SOURCE_LIST}}
                              </div>
                              <div
                                class="search-source-list"
                                ng-show="enableAutoChooseSource"
                              >
                                <div
                                  ng-repeat="item in sourceList"
                                  class="search-source"
                                >
                                  <svg
                                    class="feather"
                                    ng-show="autoChooseSourceList.indexOf(item.name) === -1"
                                    ng-click="enableSource(item.name)"
                                  >
                                    <use href="#square"></use>
                                  </svg>
                                  <svg
                                    class="feather"
                                    ng-show="autoChooseSourceList.indexOf(item.name) > -1"
                                    ng-click="disableSource(item.name)"
                                  >
                                    <use href="#check-square"></use>
                                  </svg>
                                  {{item.displayText}}
                                </div>
                              </div>
                            </div>
                            <div ng-if="isChrome" class="settings-title">
                              <span
                                >{{_CLOSE_TAB_ACTION}}({{_VALID_AFTER_RESTART}})</span
                              >
                            </div>
                            <div ng-if="isChrome" class="settings-content">
                              <div class="shortcut">
                                <svg
                                  class="feather"
                                  ng-show="!enableStopWhenClose"
                                  ng-click="setStopWhenClose(true)"
                                >
                                  <use href="#square"></use>
                                </svg>
                                <svg
                                  class="feather"
                                  ng-show="enableStopWhenClose"
                                  ng-click="setStopWhenClose(false)"
                                >
                                  <use href="#check-square"></use>
                                </svg>
                                <span style="margin-right: 20px"
                                  >{{_QUIT_APPLICATION}}</span
                                >
                                <svg
                                  class="feather"
                                  ng-show="enableStopWhenClose"
                                  ng-click="setStopWhenClose(false)"
                                >
                                  <use href="#square"></use>
                                </svg>
                                <svg
                                  class="feather"
                                  ng-show="!enableStopWhenClose"
                                  ng-click="setStopWhenClose(true)"
                                >
                                  <use href="#check-square"></use>
                                </svg>
                                <span> {{_MINIMIZE_TO_BACKGROUND}}</span>
                              </div>
                            </div>
                            <div class="settings-title">
                              <span>{{_NOWPLAYING_DISPLAY}}</span>
                            </div>
                            <div class="settings-content">
                              <div class="shortcut">
                                <svg
                                  class="feather"
                                  ng-show="!enableNowplayingCoverBackground"
                                  ng-click="setNowplayingCoverBackground(true)"
                                >
                                  <use href="#square"></use>
                                </svg>
                                <svg
                                  class="feather"
                                  ng-show="enableNowplayingCoverBackground"
                                  ng-click="setNowplayingCoverBackground(true)"
                                >
                                  <use href="#check-square"></use>
                                </svg>
                                {{_NOWPLAYING_COVER_BACKGROUND_NOTICE}}
                              </div>
                              <div class="shortcut">
                                <svg
                                  class="feather"
                                  ng-show="!enableNowplayingBitrate"
                                  ng-click="setNowplayingBitrate(true)"
                                >
                                  <use href="#square"></use>
                                </svg>
                                <svg
                                  class="feather"
                                  ng-show="enableNowplayingBitrate"
                                  ng-click="setNowplayingBitrate(true)"
                                >
                                  <use href="#check-square"></use>
                                </svg>
                                {{_NOWPLAYING_BITRATE_NOTICE}}
                              </div>
                              <div class="shortcut">
                                <svg
                                  class="feather"
                                  ng-show="!enableNowplayingPlatform"
                                  ng-click="setNowplayingPlatform(true)"
                                >
                                  <use href="#square"></use>
                                </svg>
                                <svg
                                  class="feather"
                                  ng-show="enableNowplayingPlatform"
                                  ng-click="setNowplayingPlatform(true)"
                                >
                                  <use href="#check-square"></use>
                                </svg>
                                {{_NOWPLAYING_PLATFORM_NOTICE}}
                              </div>
                            </div>
                            <div class="settings-title">
                              <span>{{_LYRIC_DISPLAY}}</span>
                            </div>
                            <div class="settings-content">
                              <div class="shortcut" ng-if="!isChrome">
                                <svg
                                  class="feather"
                                  ng-show="!enableLyricFloatingWindow"
                                  ng-click="openLyricFloatingWindow(true)"
                                >
                                  <use href="#square"></use>
                                </svg>
                                <svg
                                  class="feather"
                                  ng-show="enableLyricFloatingWindow"
                                  ng-click="openLyricFloatingWindow(true)"
                                >
                                  <use href="#check-square"></use>
                                </svg>
                                <span ng-show="enableLyricFloatingWindow"></span
                                >{{_SHOW_DESKTOP_LYRIC}}
                              </div>
                              <div class="shortcut">
                                <svg
                                  class="feather"
                                  ng-show="!enableLyricTranslation"
                                  ng-click="toggleLyricTranslation()"
                                >
                                  <use href="#square"></use>
                                </svg>
                                <svg
                                  class="feather"
                                  ng-show="enableLyricTranslation"
                                  ng-click="toggleLyricTranslation()"
                                >
                                  <use href="#check-square"></use>
                                </svg>
                                <span ng-show="enableLyricTranslation"></span
                                >{{_SHOW_LYRIC_TRANSLATION}}
                              </div>
                              <div class="shortcut" ng-if="!isChrome">
                                <svg
                                  class="feather"
                                  ng-show="!enableLyricFloatingWindowTranslation"
                                  ng-click="toggleLyricFloatingWindowTranslation()"
                                >
                                  <use href="#square"></use>
                                </svg>
                                <svg
                                  class="feather"
                                  ng-show="enableLyricFloatingWindowTranslation"
                                  ng-click="toggleLyricFloatingWindowTranslation()"
                                >
                                  <use href="#check-square"></use>
                                </svg>
                                <span
                                  ng-show="enableLyricFloatingWindowTranslation"
                                ></span
                                >{{_SHOW_DESKTOP_LYRIC_TRANSLATION}}
                              </div>
                            </div>
                            <div class="settings-title">
                              <span>{{_BACKUP_PLAYLIST}}</span>
                            </div>
                            <div class="settings-content">
                              <p>{{_BACKUP_WARNING}}</p>
                              <div>
                                <button
                                  class="btn btn-primary confirm-button"
                                  ng-click="backupMySettings()"
                                >
                                  {{_EXPORT_TO_LOCAL_FILE}}
                                </button>
                                <button
                                  class="btn btn-primary confirm-button"
                                  ng-show="githubStatus == 2"
                                  ng-click="showDialog(8)"
                                >
                                  {{_EXPORT_TO_GITHUB_GIST}}
                                </button>
                              </div>
                            </div>
                            <div class="settings-title">
                              <span>{{_RECOVER_PLAYLIST}}</span>
                            </div>
                            <div class="settings-content">
                              <p>{{_RECOVER_WARNING}}</p>
                              <label class="upload-button" for="my-file-selector">
                                <input
                                  id="my-file-selector"
                                  type="file"
                                  style="display: none"
                                  ng-model="myuploadfiles"
                                  custom-on-change="importMySettings"
                                />{{_RECOVER_FROM_LOCAL_FILE}}
                              </label>
                              <button
                                class="btn btn-warning confirm-button"
                                ng-show="githubStatus == 2"
                                ng-click="showDialog(10)"
                              >
                                {{_RECOVER_FROM_GITHUB_GIST}}
                              </button>
                            </div>

                            <div class="settings-title">
                              <span>{{_CONNECT_TO_GITHUB}}</span>
                            </div>
                            <div class="settings-content">
                              <div>
                                <p>{{_STATUS}}：{{ githubStatusText }}</p>
                                <button
                                  class="btn btn-primary confirm-button"
                                  ng-show="githubStatus == 0"
                                  ng-click="openGithubAuth(); showDialog(7);"
                                >
                                  {{_CONNECT_TO_GITHUB}}
                                </button>
                                <button
                                  class="btn btn-warning confirm-button"
                                  ng-show="githubStatus == 1"
                                  ng-click="showDialog(7);"
                                >
                                  {{_RECONNECT}}
                                </button>
                                <button
                                  class="btn btn-primary confirm-button"
                                  ng-show="githubStatus == 2"
                                  ng-click="GithubLogout();"
                                >
                                  {{_CANCEL_CONNECT}}
                                </button>
                              </div>
                            </div>

                            <div class="settings-title">
                              <span>{{_CONNECT_TO_LASTFM}}</span>
                            </div>
                            <div class="settings-content">
                              <div>
                                <p>{{_STATUS}}：{{ lastfm.getStatusText() }}</p>
                                <button
                                  class="btn btn-primary confirm-button"
                                  ng-show="!lastfm.isAuthRequested()"
                                  ng-click="lastfm.getAuth(); showDialog(4);"
                                >
                                  {{_CONNECT_TO_LASTFM}}
                                </button>
                                <button
                                  class="btn btn-warning confirm-button"
                                  ng-show="lastfm.isAuthRequested() && !lastfm.isAuthorized()"
                                  ng-click="lastfm.getAuth(); showDialog(4);"
                                >
                                  {{_RECONNECT}}
                                </button>
                                <button
                                  class="btn btn-primary confirm-button"
                                  ng-show="lastfm.isAuthRequested()"
                                  ng-click="lastfm.cancelAuth();"
                                >
                                  {{_CANCEL_CONNECT}}
                                </button>
                              </div>
                            </div>
                            <div class="settings-title">
                              <span>{{_SHORTCUTS}}</span>
                            </div>
                            <div class="settings-content">
                              <div class="shortcut_table">
                                <div class="shortcut_table-header">
                                  <div class="shortcut_table-function">
                                    {{_SHORTCUTS_FUNCTION}}
                                  </div>
                                  <div class="shortcut_table-key">
                                    {{_SHORTCUTS}}
                                  </div>
                                  <div
                                    ng-if="!isChrome"
                                    class="shortcut_table-globalkey"
                                  >
                                    {{_GLOBAL_SHORTCUTS}}
                                  </div>
                                </div>
                                <div class="shortcut_table-line">
                                  <div class="shortcut_table-function">
                                    {{_PLAY_OR_PAUSE}}
                                  </div>
                                  <div class="shortcut_table-key">p</div>
                                  <div
                                    ng-if="!isChrome"
                                    class="shortcut_table-globalkey"
                                  >
                                    Ctrl(Cmd) + Alt + {{_KEYBOARD_SPACE}}
                                  </div>
                                </div>
                                <div class="shortcut_table-line">
                                  <div class="shortcut_table-function">
                                    {{_PREVIOUS_TRACK}}
                                  </div>
                                  <div class="shortcut_table-key">[</div>
                                  <div
                                    ng-if="!isChrome"
                                    class="shortcut_table-globalkey"
                                  >
                                    Ctrl(Cmd) + Alt + ←
                                  </div>
                                </div>
                                <div class="shortcut_table-line">
                                  <div class="shortcut_table-function">
                                    {{_NEXT_TRACK}}
                                  </div>
                                  <div class="shortcut_table-key">]</div>
                                  <div
                                    ng-if="!isChrome"
                                    class="shortcut_table-globalkey"
                                  >
                                    Ctrl(Cmd) + Alt + →
                                  </div>
                                </div>
                                <div class="shortcut_table-line">
                                  <div class="shortcut_table-function">
                                    {{_VOLUME_UP}}
                                  </div>
                                  <div class="shortcut_table-key">u</div>
                                  <div
                                    ng-if="!isChrome"
                                    class="shortcut_table-globalkey"
                                  >
                                    {{_SHORTCUTS_NOT_SET}}
                                  </div>
                                </div>
                                <div class="shortcut_table-line">
                                  <div class="shortcut_table-function">
                                    {{_VOLUME_DOWN}}
                                  </div>
                                  <div class="shortcut_table-key">d</div>
                                  <div
                                    ng-if="!isChrome"
                                    class="shortcut_table-globalkey"
                                  >
                                    {{_SHORTCUTS_NOT_SET}}
                                  </div>
                                </div>
                                <!-- <div class="shortcut_table-line">
                                  <div class="shortcut_table-function">
                                    静音/取消静音
                                  </div>
                                  <div class="shortcut_table-key">m</div>
                                  <div ng-if="!isChrome" class="shortcut_table-globalkey">
                                    全局快捷键
                                  </div>
                                </div> -->
                                <div class="shortcut_table-line">
                                  <div class="shortcut_table-function">
                                    {{_QUICK_SEARCH}}
                                  </div>
                                  <div class="shortcut_table-key">f</div>
                                  <div
                                    ng-if="!isChrome"
                                    class="shortcut_table-globalkey"
                                  >
                                    {{_SHORTCUTS_NOT_SET}}
                                  </div>
                                </div>
                                <div class="shortcut_table-line">
                                  <div class="shortcut_table-function">
                                    {{ZOOM_IN_OUT}}
                                  </div>
                                  <div class="shortcut_table-key">
                                    Ctrl(Cmd) + +/-
                                  </div>
                                  <div
                                    ng-if="!isChrome"
                                    class="shortcut_table-globalkey"
                                  >
                                    {{_SHORTCUTS_NOT_SET}}
                                  </div>
                                </div>
                                <!-- <div class="shortcut_table-line">
                                  <div class="shortcut_table-function">
                                    打开/关闭播放列表
                                  </div>
                                  <div class="shortcut_table-key">l</div>
                                  <div ng-if="!isChrome" class="shortcut_table-globalkey">
                                    全局快捷键
                                  </div>
                                </div>
                                <div class="shortcut_table-line">
                                  <div class="shortcut_table-function">
                                    切换播放模式
                                  </div>
                                  <div class="shortcut_table-key">s</div>
                                  <div ng-if="!isChrome" class="shortcut_table-globalkey">
                                    全局快捷键
                                  </div>
                                </div> -->
                              </div>
                              <div
                                class="shortcut"
                                ng-if="!isChrome"
                                class="btn btn-primary confirm-button"
                              >
                                <svg
                                  class="feather"
                                  ng-show="!enableGlobalShortCut"
                                  ng-click="applyGlobalShortcut(true)"
                                >
                                  <use href="#square"></use>
                                </svg>
                                <svg
                                  class="feather"
                                  ng-show="enableGlobalShortCut"
                                  ng-click="applyGlobalShortcut(true)"
                                >
                                  <use href="#check-square"></use>
                                </svg>
                                {{_GLOBAL_SHORTCUTS_NOTICE}}
                              </div>
                            </div>
                            <div class="settings-title" ng-if="!isChrome">
                              <span>{{_PROXY_CONFIG}}</span>
                            </div>
                            <div class="settings-content" ng-if="!isChrome">
                              <span>{{_PROXY_CONFIG}}:</span>
                              {{proxyMode.displayText}}
                              <span ng-show="proxyMode.name=='custom'"
                                >{{proxyRules}}</span
                              >
                              <button ng-click="showDialog(12)">
                                {{_MODIFY}}
                              </button>
                            </div>
                            <div class="settings-title">
                              <span>{{_ABOUT}}</span>
                            </div>
                            <div class="settings-content">
                              <p>
                                Listen 1 {{_HOMEPAGE}}:
                                <a
                                  open-url="'https://listen1.github.io/listen1/'"
                                >
                                  https://listen1.github.io/listen1/
                                </a>
                              </p>
                              <p>Listen 1 {{_EMAIL}}: <EMAIL></p>
                              <p>
                                {{_FEEDBACK}}:
                                <a
                                  ng-if="isChrome"
                                  open-url="'https://github.com/listen1/listen1_chrome_extension/issues'"
                                  >https://github.com/listen1/listen1_chrome_extension/issues</a
                                >
                                <a
                                  ng-if="!isChrome"
                                  open-url="'https://github.com/listen1/listen1_desktop/issues'"
                                  >https://github.com/listen1/listen1_desktop/issues</a
                                >
                              </p>
                              <p>{{_DESIGNER}} ({{_THEME_WHITE}}): iparanoid </p>
                              <p>{{_DESIGNER}} ({{_THEME_MODERN_BLACK}}, {{_THEME_MODERN_WHITE}}): 814959822, Antion</p>
                              <p>{{_VERSION}}: v2.33.0 {{_LICENSE_NOTICE}}</p>
                              <p ng-show='lastestVersion!=""'>
                                {{_LATEST_VERSION}}: {{lastestVersion}}
                              </p>
                            </div>
                          <div style="transition: 0.3s;" ng-style="{'padding-bottom':playlist.length == 0?'13px':'120px'}"></div>
                          </div>
                        </div>
                      </div>
                      <!-- content page: 登录 -->
                      <div
                        class="page"
                        ng-show="current_tag==5 && is_window_hidden==1"
                      >
                        <div class="login">
                          <div ng-repeat="source in loginSourceList">
                            <div ng-show="is_login(source)">
                              <div style="width: 500px;background-color: var(--theme-color-hover);
                              color: var(--theme-color);cursor: default;" class="usercard">
                                <img 
                                err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png"
                                ng-src="{{musicAuth[source].avatar}}" />
                                <div class="usercard-title">
                                  <div class="usercard-nickname">
                                    {{musicAuth[source].nickname}}
                                  </div>
                                  <div ng-if="source=='netease'" class="usercard-info">网易云音乐</div>
                                  <div ng-if="source=='qq'" class="usercard-info">QQ音乐</div>
                                  <div ng-if="source=='migu'" class="usercard-info">咪咕音乐</div>
                                </div>
                                <button ng-click="logout(source)">
                                  {{_LOGOUT}}
                                </button>
                              </div>
                            </div>
                            <div ng-show="!is_login(source)">
                              <div ng-click=" openLogin(source);showDialog(11, source);" class="usercard">
                                <div class="logoin-icon">
                                <svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="sign-in-alt" class="svg-inline--fa fa-sign-in-alt fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M416 448h-84c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h84c17.7 0 32-14.3 32-32V160c0-17.7-14.3-32-32-32h-84c-6.6 0-12-5.4-12-12V76c0-6.6 5.4-12 12-12h84c53 0 96 43 96 96v192c0 53-43 96-96 96zm-47-201L201 79c-15-15-41-4.5-41 17v96H24c-13.3 0-24 10.7-24 24v96c0 13.3 10.7 24 24 24h136v96c0 21.5 26 32 41 17l168-168c9.3-9.4 9.3-24.6 0-34z"></path></svg>
                              </div>
                                <!-- <img 
                                err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png"
                                src="images/placeholder.png" /> -->

                                <div class="usercard-title">
                                  <div class="usercard-nickname">
                                    {{_NOT_LOGIN_NICKNAME}}
                                  </div>
                                  <div ng-if="source=='netease'" class="usercard-info">网易云音乐</div>
                                  <div ng-if="source=='qq'" class="usercard-info">QQ音乐</div>
                                  <div ng-if="source=='migu'" class="usercard-info">咪咕音乐</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- track list window-->
                      <div class="page">
                        <div
                          class="playlist-detail"
                          ng-show="is_window_hidden!=1 && window_type=='list'"
                        >
                          <div class="detail-head">
                            <div class="detail-head-cover">
                              <img
                                ng-src="{{ cover_img_url.replace('/300/','/500/').replace('/240/','/512/').replace('/T002R300x300M','/T002R800x800M') }}"
                                err-src="https://y.gtimg.cn/mediastyle/global/img/singer_300.png"
                              />
                              <div
                              class="covershadow"
                              style="background-image:url({{cover_img_url}});opacity: 1;"
                            ></div>
                            <div
                            class="bottom"
                            ng-click="playMylist(list_id)"
                          >  
                          <svg style="height: 44%;width:44%;margin-left: 4px;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="play" fill="currentColor" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z"></path></svg>
                          </div>
                            </div>
                            <div class="detail-head-title">
                              <h2>{{ playlist_title }}</h2>
                              <div class="playlist-button-list">
                                <div style="padding: 0;height: auto;background-color: var(--theme-color-hover);color: var(--theme-color);" class="playlist-button playadd-button">
                                  <div
                                  style="padding: 8px 0 8px 16px;width: 100px;"
                                    class="play-list"
                                    ng-click="playMylist(list_id)"
                                  >
                                  <svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="play" class="icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z"></path></svg>
                                    {{_PLAY_ALL}}
                                  </div>
                                  <div
                                  style="padding: 8px 16px 8px 0px;"
                                    class="add-list"
                                    ng-click="addMylist(list_id)"
                                  >
                                    <svg t="1659628019588" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1723" width="512" height="512"><path d="M938.516167 597.182834 85.483833 597.182834C38.527925 597.182834 0 559.256908 0 511.699001c0-46.955908 37.925926-85.483833 85.483833-85.483833l853.032334 0c46.955908 0 85.483833 37.925926 85.483833 85.483833C1024 559.256908 986.074074 597.182834 938.516167 597.182834L938.516167 597.182834 938.516167 597.182834zM512.300999 1024c-46.955908 0-85.483833-37.925926-85.483833-85.483833L426.817166 85.483833C426.817166 37.925926 464.743092 0 512.300999 0c46.955908 0 85.483833 37.925926 85.483833 85.483833l0 853.634333C597.182834 985.472075 559.256908 1024 512.300999 1024L512.300999 1024 512.300999 1024zM512.300999 1024" fill="currentColor" p-id="1724"></path></svg>
                                  </div>
                                </div>
                                <div
                                  class="playlist-button clone-button"
                                  ng-show="is_local"
                                  ng-click="addLocalMusic(list_id)"
                                >
                                  <div class="play-list">
                                    <span class="icon li-songlist"></span
                                    ><span>{{_ADD_LOCAL_SONGS}}</span>
                                  </div>
                                </div>
                                <div
                                  class="playlist-button clone-button"
                                  ng-show="!is_mine && !is_local"
                                  ng-click="clonePlaylist(list_id)"
                                >
                                  <div class="play-list">
                                    <span class="icon li-songlist"></span
                                    ><span>{{_ADD_TO_PLAYLIST}}</span>
                                  </div>
                                </div>
                                <div
                                  class="playlist-button edit-button"
                                  ng-show="is_mine && !is_local"
                                  ng-click="showDialog(3, {list_id: list_id, playlist_title: playlist_title, cover_img_url: cover_img_url})"
                                >
                                  <div class="play-list">
                                    <svg t="1659668764539" class="icon feather" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1964" width="512" height="512"><path d="M943.104 216.064q-8.192 9.216-15.36 16.384l-12.288 12.288q-6.144 6.144-11.264 10.24l-138.24-139.264q8.192-8.192 20.48-19.456t20.48-17.408q20.48-16.384 44.032-14.336t37.888 9.216q15.36 8.192 34.304 28.672t29.184 43.008q5.12 14.336 6.656 33.792t-15.872 36.864zM551.936 329.728l158.72-158.72 138.24 138.24q-87.04 87.04-158.72 157.696-30.72 29.696-59.904 58.88t-53.248 52.224-39.424 38.4l-18.432 18.432q-7.168 7.168-16.384 14.336t-20.48 12.288-31.232 12.288-41.472 13.824-40.96 12.288-29.696 6.656q-19.456 2.048-20.992-3.584t1.536-25.088q1.024-10.24 5.12-30.208t8.192-40.448 8.704-38.4 7.68-25.088q5.12-11.264 10.752-19.456t15.872-18.432zM899.072 478.208q21.504 0 40.96 10.24t19.456 41.984l0 232.448q0 28.672-10.752 52.736t-29.184 41.984-41.984 27.648-48.128 9.728l-571.392 0q-24.576 0-48.128-10.752t-41.472-29.184-29.184-43.52-11.264-53.76l0-570.368q0-20.48 11.264-42.496t29.184-39.936 40.448-29.696 45.056-11.776l238.592 0q28.672 0 40.448 20.992t11.776 42.496-11.776 41.472-40.448 19.968l-187.392 0q-21.504 0-34.816 14.848t-13.312 36.352l0 481.28q0 20.48 13.312 34.304t34.816 13.824l474.112 0q21.504 0 36.864-13.824t15.36-34.304l0-190.464q0-14.336 6.656-24.576t16.384-16.384 21.504-8.704 23.04-2.56z" fill="currentColor" p-id="1965"></path></svg>
                                    
                                    <span>{{_EDIT}}</span>
                                  </div>
                                </div>
                                <div
                                  class="playlist-button fav-button"
                                  ng-show="!is_mine && !is_local"
                                  ng-click="favoritePlaylist(list_id)"
                                  ng-class="{'favorited':is_favorite}"
                                >
                                  <div
                                    class="play-list"
                                    ng-class="{'favorited':is_favorite,'notfavorite':!is_favorite}"
                                  >
                                  <svg ng-if="!is_favorite" t="1659664836199" class="icon feather" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1992" width="512" height="512"><path d="M550.315 822.582l229.558 125.937-43.042-266.223c-4.783-30.289 4.782-62.172 25.506-82.896l183.328-188.11-256.659-39.853c-28.694-3.188-54.2-23.912-68.548-51.013l-111.59-242.31L395.682 318.83c-12.754 28.695-38.26 47.825-68.549 51.013L70.476 411.29l184.921 189.704c22.319 20.724 30.29 52.607 25.507 82.895l-41.448 264.63 229.558-125.938c23.912-15.942 55.795-15.942 81.301 0z m-49.418 65.36L271.339 1013.88c-36.665 19.13-79.708 6.376-97.243-31.883-7.97-14.348-11.16-31.883-7.97-47.825l43.041-264.629c1.594-6.377-1.594-12.753-4.782-17.536L22.652 465.492c-28.695-28.695-28.695-76.52-1.595-106.808 11.16-11.16 25.507-19.13 41.448-22.318l256.658-39.854c4.783 0 11.16-6.377 14.348-9.565l113.184-242.31c17.536-38.26 60.578-52.608 97.244-33.478 14.347 7.97 25.506 19.13 31.883 33.477l111.59 242.311c1.594 6.377 7.97 9.565 14.348 9.565l256.658 39.854c39.853 6.376 66.954 43.042 60.577 84.49-1.594 17.535-9.564 31.883-22.318 43.042L814.944 653.602c-3.188 3.188-4.782 11.159-4.782 17.535l43.042 266.223c7.97 41.448-19.13 79.708-58.984 86.084-15.941 1.595-31.883 0-44.636-7.97L520.026 889.536c-6.376-3.188-14.347-3.188-19.13-1.594z"  fill="currentColor" p-id="1993"></path></svg>
                                  <svg ng-if="is_favorite"  t="1659665365620" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2146" width="512" height="512"><path d="M961.161 334.715l-255.883-39.982c-7.996 0-12.794-4.798-14.393-9.596L578.935 42.048c-7.996-14.393-19.19-27.187-31.985-33.585-36.783-19.19-79.963-4.797-97.555 33.585l-113.548 241.49c-4.798 4.798-7.997 9.596-14.394 9.596L62.372 333.115c-15.993 4.798-30.387 11.195-41.581 22.39-27.188 30.386-27.188 79.964 1.599 107.151L204.707 651.37c4.797 4.798 6.397 11.195 6.397 15.993l-43.18 265.479c-4.798 14.393 0 31.985 6.396 47.978 17.592 36.783 60.773 51.176 97.556 31.985L502.17 886.463c6.397-1.6 12.794-1.6 19.191 0l228.696 126.342c12.794 7.996 28.787 9.596 44.78 7.996 39.981-7.996 67.169-44.78 60.771-86.36l-43.18-265.479c0-6.397 1.6-12.794 4.798-15.993l185.515-188.714c12.795-11.194 20.79-27.187 20.79-43.18 4.799-41.58-22.389-79.963-62.37-86.36z" fill="currentColor"  p-id="2147"></path></svg>  
                                
                                    <span
                                      >{{is_favorite?_FAVORITED:_FAVORITE}}</span
                                    >
                                  </div>
                                </div>
                                <div
                                  class="playlist-button edit-button"
                                  ng-show="isChrome && is_favorite && !is_local"
                                  ng-click="closeWindow();showPlaylist(list_id)"
                                >
                                  <div class="play-list">
                                    
                                    <svg t="1659669143423" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2024" width="512" height="512"><path d="M684.032 403.456q-17.408-8.192-15.872-22.016t11.776-22.016q3.072-2.048 19.968-15.872t41.472-33.28q-43.008-49.152-102.4-77.312t-129.024-28.16q-64.512 0-120.832 24.064t-98.304 66.048-66.048 98.304-24.064 120.832q0 63.488 24.064 119.808t66.048 98.304 98.304 66.048 120.832 24.064q53.248 0 100.864-16.896t87.04-47.616 67.584-72.192 41.472-90.624q7.168-23.552 26.624-38.912t46.08-15.36q31.744 0 53.76 22.528t22.016 53.248q0 14.336-5.12 27.648-21.504 71.68-63.488 132.096t-99.84 103.936-128.512 68.096-148.48 24.576q-95.232 0-179.2-35.84t-145.92-98.304-98.304-145.92-36.352-178.688 36.352-179.2 98.304-145.92 145.92-98.304 179.2-36.352q105.472 0 195.584 43.52t153.6 118.272q23.552-17.408 39.424-30.208t19.968-15.872q6.144-5.12 13.312-7.68t13.312 0 10.752 10.752 6.656 24.576q1.024 9.216 2.048 31.232t2.048 51.2 1.024 60.416-1.024 58.88q-1.024 34.816-16.384 50.176-8.192 8.192-24.576 9.216t-34.816-3.072q-27.648-6.144-60.928-13.312t-63.488-14.848-53.248-14.336-29.184-9.728z" fill="currentColor" p-id="2025"></path></svg>
                                    <span>{{_REFRESH_PLAYLIST}}</span>
                                  </div>
                                </div>
                                <div
                                  class="playlist-button edit-button"
                                  ng-show="!is_mine && !is_local"
                                  open-url="playlist_source_url"
                                >
                                  <div class="play-list">
                                    
                                    <svg t="1659628952356" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1783" width="512" height="512"><path d="M475.66342293 318.38570027A54.23369707 54.23369707 0 0 1 399.7362464 241.91618667L512 128.56775893a271.16848747 271.16848747 0 0 1 383.43224107 383.43224107L779.372128 625.8907648a54.23369707 54.23369707 0 1 1-76.46951253-76.4695136l113.89076373-113.8907648a162.70109227 162.70109227 0 0 0-229.9508768-229.9508768z m74.8425024 385.60158826A54.23369707 54.23369707 0 1 1 626.97543893 779.372128l-114.43310186 114.43310187A271.16848747 271.16848747 0 0 1 128.56775893 512L243.0008608 399.7362464a54.23369707 54.23369707 0 1 1 77.01185067 74.30016533l-114.97543894 114.43310187a162.70109227 162.70109227 0 0 0 229.9508768 229.9508768z m-135.0419072-17.89712a54.23369707 54.23369707 0 1 1-76.46951253-76.4695136l268.45680213-268.45680213a54.23369707 54.23369707 0 0 1 76.4695136 76.4695136z" fill="currentColor" p-id="1784"></path></svg>
                                    <span>{{_ORIGIN_LINK}}</span>
                                  </div>
                                </div>
                                <div
                                  class="playlist-button edit-button"
                                  ng-show="is_mine && !is_local"
                                  ng-click="showDialog(6)"
                                >
                                  <div class="play-list">
                                    <svg t="1659668939470" class="icon feather" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2273" width="512" height="512"><path d="M725.333333 938.666667H298.666667a256 256 0 0 1-256-256V341.333333h128v298.666667a170.666667 170.666667 0 0 0 170.666666 170.666667h341.333334a170.666667 170.666667 0 0 0 170.666666-170.666667V341.333333h128v341.333334a256 256 0 0 1-256 256zM256 426.666667h128V213.333333h256v213.333334h128l-256 256z m128-341.333334h256v85.333334H384V85.333333z" fill="currentColor" p-id="2274"></path></svg>
                                  
                                    <span>{{_IMPORT}}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <ul class="detail-songlist playlist-songlist"  ng-style="{'padding-bottom':playlist.length == 0?'13px':'120px'}">
                            <div class="playlist-search">
                              <svg  fill="currentColor" style="opacity: 0.28;margin-right: 4px;width: 15px;height: 15px;cursor: default;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="search" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"></path></svg>
                              <input
                                class="form-control playlist-search-input"
                                id="search-input"
                                type="text"
                                ng-model="playlistFilter.key"
                                placeholder="{{_SEARCH_PLAYLIST}}"
                              />
                            </div>
                            <!-- <div class="playlist-search">
                              <svg class="feather playlist-search-icon">
                                <use href="#search"></use>
                              </svg>
                              <svg
                                class="feather playlist-clear-icon"
                                ng-show="playlistFilter.key!=''"
                                ng-click="clearFilter()"
                              >
                                <use href="#x"></use>
                              </svg>
                              <input
                                class="playlist-search-input"
                                type="text"
                                ng-model="playlistFilter.key"
                                placeholder="{{_SEARCH_PLAYLIST}}"
                              />
                            </div> -->
                            <!-- <li class="head">
                              <div class="title">
                                <a>{{_SONGS + '(' + songs.length + ')'}}</a>
                              </div>
                              <div class="artist"><a>{{_ARTISTS}}</a></div>
                              <div class="album"><a>{{_ALBUMS}}</a></div>
                              <div class="tools">{{_OPERATION}}</div>
                            </li> -->
                            <li
                              class="isSearchType"
                              ng-class="{ 'playing': currentPlaying.id == song.id }"
                              ng-repeat="song in songs | filter: fieldFilter track by $index"
                              ng-mouseenter="options=true"
                              ng-mouseleave="options=false"
                              ng-dblclick="addAndPlay(song)"
                              draggable="true"
                              drag-drop-zone
                              drag-zone-object="song"
                              drag-zone-title="song.title"
                              sortable="is_mine || is_local"
                              drag-zone-type="'application/listen1-song'"
                              drop-zone-ondrop="onPlaylistSongDrop(list_id, song, arg1, arg2, arg3)"
                            >
                            <img err-src="https://y.gtimg.cn/mediastyle/global/img/playlist_300.png" add-and-play="song" ng-src="{{ song.img_url }}?param=224y224" alt="">
                            <div class="title-and-artist">
                            <div class="container">
                              <div class="title">
                                <!-- <a class="disabled" ng-if="song.disabled" ng-click="copyrightNotice()">{{ song.title }}</a> -->
                                <a add-and-play="song">{{ song.title }}</a>
                              </div>
                              <div class="artist">
                                <a ng-click="showPlaylist(song.artist_id)"
                                  >{{ song.artist }}</a
                                >
                              </div>
                            </div>
                          </div>
                              <div class="album">
                                <a ng-click="showPlaylist(song.album_id)"
                                  >{{ song.album }}</a
                                >
                              </div>
                              <div class="tools">
                                <a
                                  title="{{_ADD_TO_QUEUE}}"
                                  class="detail-add-button"
                                  add-without-play="song"
                                  ng-show="options"
                                  >
                                <svg t="1659628019588" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1723" width="512" height="512"><path d="M938.516167 597.182834 85.483833 597.182834C38.527925 597.182834 0 559.256908 0 511.699001c0-46.955908 37.925926-85.483833 85.483833-85.483833l853.032334 0c46.955908 0 85.483833 37.925926 85.483833 85.483833C1024 559.256908 986.074074 597.182834 938.516167 597.182834L938.516167 597.182834 938.516167 597.182834zM512.300999 1024c-46.955908 0-85.483833-37.925926-85.483833-85.483833L426.817166 85.483833C426.817166 37.925926 464.743092 0 512.300999 0c46.955908 0 85.483833 37.925926 85.483833 85.483833l0 853.634333C597.182834 985.472075 559.256908 1024 512.300999 1024L512.300999 1024 512.300999 1024zM512.300999 1024" fill="currentColor" p-id="1724"></path></svg>
                              </a>
                                <a
                                  title="{{_ADD_TO_PLAYLIST}}"
                                  class="detail-fav-button"
                                  ng-show="options"
                                  ng-click="showDialog(0, song)"
                                  >
                                  <svg t="1659628805392" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6942" width="512" height="512"><path d="M768 768v192h-128v-192h-192v-128h192v-192h128v192h192v128h-192z m64-576H192v640h192v128H64V64h896v320h-128V192z" fill="currentColor" p-id="6943"></path></svg>
                                  </a>
                                <a
                                  title="{{_REMOVE_FROM_PLAYLIST}}"
                                  class="detail-delete-button"
                                  ng-click="removeSongFromPlaylist(song, list_id)"
                                  ng-show="options && (is_mine=='1'||is_local) "
                                  >
                                  <svg t="1659670048675" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2024" width="512" height="512"><path d="M895.464448 119.006208 677.967872 119.006208c0 0-32.8448 1.020928-58.648576-26.943488-10.395648-12.050432-27.804672-23.795712-56.4224-24.799232l-41.183232 0-6.280192 0-41.182208 0c-28.618752 1.004544-46.031872 12.749824-56.4224 24.799232-25.807872 27.964416-58.6496 26.943488-58.6496 26.943488L141.682688 119.006208c-13.99296 0-25.33888 11.34592-25.33888 25.33888l0 93.090816c-0.053248 26.927104 26.083328 26.396672 26.083328 26.396672l49.83808 0L192.265216 913.65376c0 0-3.966976 44.084224 40.121344 46.45888l269.31712 0 33.738752 0 30.808064 0.238592 38.500352 0 174.934016 0 24.297472 0 0.782336-0.238592c44.080128-2.374656 40.117248-46.45888 40.117248-46.45888L844.88192 263.832576l49.842176 0c0 0 26.133504 0.530432 26.083328-26.396672l0-93.090816C920.8064 130.353152 909.46048 119.006208 895.464448 119.006208zM430.539776 803.171328c0 17.042432-13.828096 30.865408-30.865408 30.865408-17.042432 0-30.865408-13.824-30.865408-30.865408L368.80896 320.736256c0-17.042432 13.824-30.865408 30.865408-30.865408 17.038336 0 30.865408 13.824 30.865408 30.865408L430.539776 803.171328zM663.436288 803.171328c0 17.042432-13.824 30.865408-30.865408 30.865408-17.038336 0-30.865408-13.824-30.865408-30.865408L601.705472 320.736256c0-17.042432 13.828096-30.865408 30.865408-30.865408 17.041408 0 30.865408 13.824 30.865408 30.865408L663.436288 803.171328z" fill="currentColor" p-id="2025"></path></svg>
                                  </a>
                                <a
                                  title="{{_ORIGIN_LINK}}"
                                  class="source-button"
                                  open-url="song.source_url"
                                  ng-show="options && !is_local"
                                  >
                                  <svg t="1659628952356" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1783" width="512" height="512"><path d="M475.66342293 318.38570027A54.23369707 54.23369707 0 0 1 399.7362464 241.91618667L512 128.56775893a271.16848747 271.16848747 0 0 1 383.43224107 383.43224107L779.372128 625.8907648a54.23369707 54.23369707 0 1 1-76.46951253-76.4695136l113.89076373-113.8907648a162.70109227 162.70109227 0 0 0-229.9508768-229.9508768z m74.8425024 385.60158826A54.23369707 54.23369707 0 1 1 626.97543893 779.372128l-114.43310186 114.43310187A271.16848747 271.16848747 0 0 1 128.56775893 512L243.0008608 399.7362464a54.23369707 54.23369707 0 1 1 77.01185067 74.30016533l-114.97543894 114.43310187a162.70109227 162.70109227 0 0 0 229.9508768 229.9508768z m-135.0419072-17.89712a54.23369707 54.23369707 0 1 1-76.46951253-76.4695136l268.45680213-268.45680213a54.23369707 54.23369707 0 0 1 76.4695136 76.4695136z" fill="currentColor" p-id="1784"></path></svg>
                                </a>
                              </div>
                              <div class="time">{{song.duration}}</div>
                            </li>
                          </ul>
                        </div>
                        <!-- now playing window-->
                      
                      </div>
                    </div>
                  </div>
                </div>
                <div class="footer" ng-class="{footerdef:playlist.length == 0}">
                  
                  <div class="footer-main" ng-style="{background}"  ng-class="{slidedown: window_type=='track'}">
                    <div class="bgwrapper" ng-class="{slidedown: window_type=='track'}" style="overflow:hidden ;">
                      <div
                      ng-if="enableNowplayingCoverBackground"
                      class="bg"
                      ng-style="{'background-image': 'url(' + currentPlaying.img_url + ')'}"
                    ></div>
                    </div>
                    <div
                  ng-class="{slidedown: window_type!='track'}"
                  class="songdetail-wrapper"
                >
                  <div class="draggable-zone"></div>
                  
                  <div
                    class="close"
                    ng-class="isMac? 'mac':'' "
                    ng-click="popWindow()"
                  >
                  <svg style="width: 19px;height:19px;" enable-background="new 0 0 32 32" height="32px" version="1.1" viewBox="0 0 32 32" width="32px" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M14.77,23.795L5.185,14.21c-0.879-0.879-0.879-2.317,0-3.195l0.8-0.801c0.877-0.878,2.316-0.878,3.194,0  l7.315,7.315l7.316-7.315c0.878-0.878,2.317-0.878,3.194,0l0.8,0.801c0.879,0.878,0.879,2.316,0,3.195l-9.587,9.585  c-0.471,0.472-1.104,0.682-1.723,0.647C15.875,24.477,15.243,24.267,14.77,23.795z" fill="currentColor"/></svg>
                  </div>

                  <div ng-if="!isChrome && !isMac" class="window-control">
                    <svg class="icon" window-control="window_min">
                      <use href="#minimize-2"></use>
                    </svg>
                    <svg class="icon" window-control="window_max">
                      <use href="#maximize"></use>
                    </svg>
                    <svg class="icon" window-control="window_close">
                      <use href="#x"></use>
                    </svg>
                  </div>

                  <div class="playsong-detail">
                    <div class="detail-head">
                      <div>
                      <div class="detail-head-cover">
                        <div ng-if="window_type=='track'"  class="covershadow" style="background-image:url({{currentPlaying.img_url}}?param=224y224);opacity: 1;z-index: -1;"></div>
                        <img
                      
                        id="trackbackground" 
                          ng-src="{{ currentPlaying.img_url.replace('224y224','1024y1024').replace('/120/','/1024/').replace('/T002R300x300M','/T002R800x800M') }}?param=1024y1024"
                          err-src="https://y.gtimg.cn/mediastyle/global/img/album_300.png"
                        />
                      </div>
                      <div class="detail-head-title">
                        <div class="title">
                          <h2>{{ currentPlaying.title }}</h2>
                          <span
                            class="badge"
                            ng-if="enableNowplayingBitrate && currentPlaying.bitrate !== undefined"
                            >{{ currentPlaying.bitrate }}</span
                          >
                          <span
                            class="badge platform"
                            ng-if="enableNowplayingPlatform && currentPlaying.platform !== undefined"
                            >{{ currentPlaying.platformText }}</span
                          >
                        </div>
                        <div class="info">
                          <div class="singer">
                            <a
                              ng-click="showPlaylist(currentPlaying.artist_id)"
                              title="{{currentPlaying.artist}}"
                              >{{ currentPlaying.artist }}</a
                            >
                          </div>
                          <span>-</span>
                          <div class="album">
                            <a
                              ng-click="showPlaylist(currentPlaying.album_id)"
                              title="{{currentPlaying.album}}"
                              >{{ currentPlaying.album }}</a
                            >
                          </div>
                        </div>
                      </div>
                        <!-- <a title="加入收藏" class="clone" ng-click="showDialog(0, currentPlaying)">收藏</a>
          <a open-url="currentPlaying.source_url" title="原始链接" class="link">原始链接</a> -->
                      </div>
                    </div>
                    <div class="detail-songinfo">
                      
                      <div class="lyric">
                        <div class="placeholder"></div>
                        <p
                          ng-repeat="line in lyricArray track by $index"
                          data-line="{{line.lineNumber}}"
                          ng-class="{ 'highlight': (line.lineNumber == lyricLineNumber) || (line.lineNumber == lyricLineNumberTrans) , hide: (line.translationFlag && !enableLyricTranslation), translate: line.translationFlag}"
                        >
                          {{ line.content }}
                        </p>
                        <div class="placeholder"></div>
                      </div>
                    </div>
                  </div>
                </div>
                    <div class="footerwrap">
                    <div class="left-control" ng-class="{slidedown: window_type=='track'}">
                  
                    <div class="playlist-toggle">
                      <span
                      ng-class="{playlistactive: !menuHidden}"
                        ng-click="togglePlaylist()"
                        class="icon"
                      >
                      <svg style="width: 19px;height: 19px;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="list-music" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" d="M16 256h256a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16H16a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16zm0-128h256a16 16 0 0 0 16-16V80a16 16 0 0 0-16-16H16A16 16 0 0 0 0 80v32a16 16 0 0 0 16 16zm128 192H16a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h128a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16zM470.94 1.33l-96.53 28.51A32 32 0 0 0 352 60.34V360a148.76 148.76 0 0 0-48-8c-61.86 0-112 35.82-112 80s50.14 80 112 80 112-35.82 112-80V148.15l73-21.39a32 32 0 0 0 23-30.71V32a32 32 0 0 0-41.06-30.67z" class=""></path></svg>
                    </span>
                    </div>

                    <div class="splitter"></div>
                    
                    <div class="detail" ng-if="playlist.length > 0">

                      <div class="title">
                        <span
                          ng-if="currentPlaying.source === 'xiami'"
                          style="color: orange; font-size: medium"
                          >⚠️ </span
                        >{{ currentPlaying.title }}
                      </div>
                      <div class="more-info">
                        <div class="singer">
                          <a ng-click="showPlaylist(currentPlaying.artist_id)"
                            >{{ currentPlaying.artist }}</a
                          >
                          -
                          <a ng-click="showPlaylist(currentPlaying.album_id)"
                            >{{ currentPlaying.album }}</a
                          >
                        </div>
                      </div>
                    
                    </div>
                  </div>
                  <div class="main-info">   
                    <div
                      class="cover"
                      class="cover"
                      ng-if="playlist.length > 0"
                    >
                    <!-- <div class="logo-banner" ng-if="playlist.length == 0">
                      <svg
                        class="logo"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="#666666"
                        stroke="#666666"
                        stroke-width="1"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <polygon
                          points="7 4 7 19 16 19 16 16 10 16 10 4"
                        ></polygon>
                        <polygon points="13 4 13 13 16 13 16 4"></polygon>
                      </svg>
                    </div> -->
                  
                    <ul id="cover-list" class="cover-list">
                      <li ng-class="{
                        lipause:!isPlaying,
                        liplay:isPlaying,
                        rotatecircl:song.stageId === getSongIdByIndex(currentIndex),
                        a:song.stageId === getSongIdByIndex(currentIndex-1),
                        b:song.stageId === getSongIdByIndex(currentIndex),
                        c:song.stageId === getSongIdByIndex(currentIndex+1),
                        def:song.stageId === getSongIdByIndex(currentIndex+2)||song.stageId === getSongIdByIndex(currentIndex-2)}" class="hid"
                            id="song{{ song.id }}" ng-repeat="song in staged_playlist track by song.stageId" draggable="true">
                    <img
                    err-src="https://y.gtimg.cn/mediastyle/global/img/album_300.png"
                    ng-src="{{song.img_url.replace('/120/','/300/')}}?param=300y300" alt="">
                    </li>
                    </ul>
                    <div class="cover-list">
                      <span class="a" ng-class="{show: settings.playmode===1}" prev-track="">
                        <svg style="width:20px ;height: 20px;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="step-backward" fill="currentColor" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M64 468V44c0-6.6 5.4-12 12-12h48c6.6 0 12 5.4 12 12v176.4l195.5-181C352.1 22.3 384 36.6 384 64v384c0 27.4-31.9 41.7-52.5 24.6L136 292.7V468c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12z"></path></svg>
                      </span>
                    <span
                      class="b"
                      play-pause-toggle=""
                    >
                    <svg style="width:30px ;height: 30px;" ng-show="isPlaying" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="pause" fill="currentColor" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M144 479H48c-26.5 0-48-21.5-48-48V79c0-26.5 21.5-48 48-48h96c26.5 0 48 21.5 48 48v352c0 26.5-21.5 48-48 48zm304-48V79c0-26.5-21.5-48-48-48h-96c-26.5 0-48 21.5-48 48v352c0 26.5 21.5 48 48 48h96c26.5 0 48-21.5 48-48z"></path></svg>
                    <svg style="width:30px ;height: 30px;" ng-show="!isPlaying" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="play" fill="currentColor" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z"></path></svg>
                  </span>
                    <span class="c" ng-class="{show: settings.playmode===1}" next-track="">
                      <svg style="width:20px ;height: 20px;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="step-forward" fill="currentColor" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M384 44v424c0 6.6-5.4 12-12 12h-48c-6.6 0-12-5.4-12-12V291.6l-195.5 181C95.9 489.7 64 475.4 64 448V64c0-27.4 31.9-41.7 52.5-24.6L312 219.3V44c0-6.6 5.4-12 12-12h48c6.6 0 12 5.4 12 12z"></path></svg>
                    </span>
                    </div>
                    
                      <!-- <img
                      play-pause-toggle=""
                        ng-src="{{ currentPlaying.img_url.replace('/120/','/500/').replace('/T002R300x300M','/T002R500x500M')  }}?param=512y512"
                        err-src="https://y.gtimg.cn/mediastyle/global/img/album_300.png"
                      /> -->
                      <div id="rotatemark"
                      
                      class="circlemark">
                        <div id="circlmark" ng-style="{transform :'rotate(-'+ myProgress/100*180  + 'deg)' }" class="circle">
                          <div class="topmark">
                            <div class="top"></div>
                          </div>
                          <div class="bottom">
                            <div class="bottomcircle"></div>
                          </div>
                        </div>
                      </div>    
                    </div>
                  
                    <div ng-show="playlist.length != 0" class="footertime">
                      <div class="bottomprogressbar">    
                        <div class="playbar">
                          <span class="icon">
                            <svg style="width:18px ;height:18px;" t="1659244374154" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1286" width="512" height="512"><path d="M776.877181 172.523285l-3.41170299-3.38305C706.25063399 102.782921 613.924879 61.763692 511.992325 61.763692c-101.81385001 0-194.07923001 40.930201-261.276678 107.181091l-3.790326 3.773953c-66.236564 67.228147-107.181091 159.44850199-107.18109101 261.292028 0 101.93255399 41.004903 194.243983 107.37654301 261.473153l3.3984 3.384074 261.486456 261.516132 261.66860499-261.651208 3.02182301-3.052522c66.477041-67.257823 107.556645-159.628604 107.55664499-261.667581C884.253724 332.092537 843.249845 239.782132 776.877181 172.523285zM512.006651 539.79402c-71.934333 0-130.247436-58.32742999-130.247436-130.247436 0-29.728068 9.924024-57.078996 26.64587-78.986959 23.80210201-31.17092999 61.349253-51.290153 103.601566-51.290153 71.935356 0 130.247436 58.34278 130.247436 130.278135C642.254088 481.46659001 583.942007 539.79402 512.006651 539.79402z" fill="currentColor" p-id="1287"></path></svg>   
                          </span>
                          <div 
                            class="playbar-clickable"
                            id="progressbar"
                            mode="play"
                            draggable-bar=""
                          >
                            <div class="barbg">
                              <div
                                class="cur"
                                ng-style="{width :( myProgress)+ '%' }"
                              >
                                <span class="btn"><i></i></span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="volume-ctrl" volume-wheel="">
                          <span
                            class="icon"
                            ng-click="toggleMuteStatus()"
                          >
                            <svg ng-show="mute" style="width:18px ;height:18px;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="volume-mute"  fill="currentColor" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M215.03 71.05L126.06 160H24c-13.26 0-24 10.74-24 24v144c0 13.25 10.74 24 24 24h102.06l88.97 88.95c15.03 15.03 40.97 4.47 40.97-16.97V88.02c0-21.46-25.96-31.98-40.97-16.97zM461.64 256l45.64-45.64c6.3-6.3 6.3-16.52 0-22.82l-22.82-22.82c-6.3-6.3-16.52-6.3-22.82 0L416 210.36l-45.64-45.64c-6.3-6.3-16.52-6.3-22.82 0l-22.82 22.82c-6.3 6.3-6.3 16.52 0 22.82L370.36 256l-45.63 45.63c-6.3 6.3-6.3 16.52 0 22.82l22.82 22.82c6.3 6.3 16.52 6.3 22.82 0L416 301.64l45.64 45.64c6.3 6.3 16.52 6.3 22.82 0l22.82-22.82c6.3-6.3 6.3-16.52 0-22.82L461.64 256z"></path></svg>
                            <svg ng-show="!mute" style="width:18px ;height:18px;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="volume" fill="currentColor" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 480 512" ><path fill="currentColor" d="M215.03 71.05L126.06 160H24c-13.26 0-24 10.74-24 24v144c0 13.25 10.74 24 24 24h102.06l88.97 88.95c15.03 15.03 40.97 4.47 40.97-16.97V88.02c0-21.46-25.96-31.98-40.97-16.97zM480 256c0-63.53-32.06-121.94-85.77-156.24-11.19-7.14-26.03-3.82-33.12 7.46s-3.78 26.21 7.41 33.36C408.27 165.97 432 209.11 432 256s-23.73 90.03-63.48 115.42c-11.19 7.14-14.5 22.07-7.41 33.36 6.51 10.36 21.12 15.14 33.12 7.46C447.94 377.94 480 319.53 480 256zm-141.77-76.87c-11.58-6.33-26.19-2.16-32.61 9.45-6.39 11.61-2.16 26.2 9.45 32.61C327.98 228.28 336 241.63 336 256c0 14.38-8.02 27.72-20.92 34.81-11.61 6.41-15.84 21-9.45 32.61 6.43 11.66 21.05 15.8 32.61 9.45 28.23-15.55 45.77-45 45.77-76.88s-17.54-61.32-45.78-76.86z" class=""></path></svg>
                          </span>
                          <div 
                            class="m-pbar volume"
                            id="volumebar"
                            mode="volume"
                            draggable-bar="">
                            <div class="barbg">
                              <div class="cur" ng-style="{width : volume + '%' }">
                                <span class="btn"><i></i></span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="timeswitch">
                      <span class="current">{{ currentPosition }}</span>
                      <span style="font-weight: 700;"> / </span>
                      <span class="total">{{ currentDuration }}</span>
                    </div>
                    </div>  
                  </div>
                  <div class="right-control">
                    
                    <div class="ctrl">
                      <a
                        ng-click="showDialog(0, currentPlaying)"
                        title="{{_ADD_TO_PLAYLIST}}"
                      >
                        <span class="icon">
                          <svg style="width: 18px;height: 18px;"  fill="currentColor"  t="1659233308084" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1133" width="512" height="512"><path d="M938.516167 597.182834 85.483833 597.182834C38.527925 597.182834 0 559.256908 0 511.699001c0-46.955908 37.925926-85.483833 85.483833-85.483833l853.032334 0c46.955908 0 85.483833 37.925926 85.483833 85.483833C1024 559.256908 986.074074 597.182834 938.516167 597.182834L938.516167 597.182834 938.516167 597.182834zM512.300999 1024c-46.955908 0-85.483833-37.925926-85.483833-85.483833L426.817166 85.483833C426.817166 37.925926 464.743092 0 512.300999 0c46.955908 0 85.483833 37.925926 85.483833 85.483833l0 853.634333C597.182834 985.472075 559.256908 1024 512.300999 1024L512.300999 1024 512.300999 1024zM512.300999 1024"  fill="currentColor" p-id="1134"></path></svg>
                        </span>
                      </a>
                      <a
                        title="{{ settings.playmode | playmode_title }}(s)"
                        ng-click="changePlaymode()"
                      >
                        <span
                          ng-show="settings.playmode == 0"
                          class="icon"
                        >
                        <span  class="dn color-inherit link hover-pink">
                          <svg style="width: 18px;height: 18px;"  aria-hidden="true" focusable="false" data-prefix="fas" data-icon="repeat" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"  fill="currentColor"><path fill="currentColor" d="M512 256c0 88.224-71.775 160-160 160H170.067l34.512 32.419c9.875 9.276 10.119 24.883.539 34.464l-10.775 10.775c-9.373 9.372-24.568 9.372-33.941 0l-92.686-92.686c-9.373-9.373-9.373-24.568 0-33.941l92.686-92.686c9.373-9.373 24.568-9.373 33.941 0l10.775 10.775c9.581 9.581 9.337 25.187-.539 34.464L170.067 352H352c52.935 0 96-43.065 96-96 0-13.958-2.996-27.228-8.376-39.204-4.061-9.039-2.284-19.626 4.723-26.633l12.183-12.183c11.499-11.499 30.965-8.526 38.312 5.982C505.814 205.624 512 230.103 512 256zM72.376 295.204C66.996 283.228 64 269.958 64 256c0-52.935 43.065-96 96-96h181.933l-34.512 32.419c-9.875 9.276-10.119 24.883-.539 34.464l10.775 10.775c9.373 9.372 24.568 9.372 33.941 0l92.686-92.686c9.373-9.373 9.373-24.568 0-33.941l-92.686-92.686c-9.373-9.373-24.568-9.373-33.941 0L306.882 29.12c-9.581 9.581-9.337 25.187.539 34.464L341.933 96H160C71.775 96 0 167.776 0 256c0 25.897 6.186 50.376 17.157 72.039 7.347 14.508 26.813 17.481 38.312 5.982l12.183-12.183c7.008-7.008 8.786-17.595 4.724-26.634z" class=""></path></svg></span>
                      </span>
                        <span
                          ng-show="settings.playmode == 1"
                          class="icon"
                        >
                        <svg style="width: 18px;height: 18px;"  aria-hidden="true" focusable="false" data-prefix="fas" data-icon="random" fill="currentColor" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M504.971 359.029c9.373 9.373 9.373 24.569 0 33.941l-80 79.984c-15.01 15.01-40.971 4.49-40.971-16.971V416h-58.785a12.004 12.004 0 0 1-8.773-3.812l-70.556-75.596 53.333-57.143L352 336h32v-39.981c0-21.438 25.943-31.998 40.971-16.971l80 79.981zM12 176h84l52.781 56.551 53.333-57.143-70.556-75.596A11.999 11.999 0 0 0 122.785 96H12c-6.627 0-12 5.373-12 12v56c0 6.627 5.373 12 12 12zm372 0v39.984c0 21.46 25.961 31.98 40.971 16.971l80-79.984c9.373-9.373 9.373-24.569 0-33.941l-80-79.981C409.943 24.021 384 34.582 384 56.019V96h-58.785a12.004 12.004 0 0 0-8.773 3.812L96 336H12c-6.627 0-12 5.373-12 12v56c0 6.627 5.373 12 12 12h110.785c3.326 0 6.503-1.381 8.773-3.812L352 176h32z"></path></svg>
                      </span>
                        <span
                          ng-show="settings.playmode == 2"
                          class="icon"
                        >
                        <span  class="dn color-inherit link hover-indigo"><svg style="width: 18px;height: 18px;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="repeat-1" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"  fill="currentColor"><path fill="currentColor" d="M512 256c0 88.224-71.775 160-160 160H170.067l34.512 32.419c9.875 9.276 10.119 24.883.539 34.464l-10.775 10.775c-9.373 9.372-24.568 9.372-33.941 0l-92.686-92.686c-9.373-9.373-9.373-24.568 0-33.941l80.269-80.27c9.373-9.373 24.568-9.373 33.941 0l10.775 10.775c9.581 9.581 9.337 25.187-.539 34.464l-22.095 20H352c52.935 0 96-43.065 96-96 0-13.958-2.996-27.228-8.376-39.204-4.061-9.039-2.284-19.626 4.723-26.633l12.183-12.183c11.499-11.499 30.965-8.526 38.312 5.982C505.814 205.624 512 230.103 512 256zM72.376 295.204C66.996 283.228 64 269.958 64 256c0-52.935 43.065-96 96-96h181.933l-22.095 20.002c-9.875 9.276-10.119 24.883-.539 34.464l10.775 10.775c9.373 9.372 24.568 9.372 33.941 0l80.269-80.27c9.373-9.373 9.373-24.568 0-33.941l-92.686-92.686c-9.373-9.373-24.568-9.373-33.941 0l-10.775 10.775c-9.581 9.581-9.337 25.187.539 34.464L341.933 96H160C71.775 96 0 167.776 0 256c0 25.897 6.186 50.376 17.157 72.039 7.347 14.508 26.813 17.481 38.312 5.982l12.183-12.183c7.008-7.008 8.786-17.595 4.724-26.634zm154.887 4.323c0-7.477 3.917-11.572 11.573-11.572h15.131v-39.878c0-5.163.534-10.503.534-10.503h-.356s-1.779 2.67-2.848 3.738c-4.451 4.273-10.504 4.451-15.666-1.068l-5.518-6.231c-5.342-5.341-4.984-11.216.534-16.379l21.72-19.939c4.449-4.095 8.366-5.697 14.42-5.697h12.105c7.656 0 11.749 3.916 11.749 11.572v84.384h15.488c7.655 0 11.572 4.094 11.572 11.572v8.901c0 7.477-3.917 11.572-11.572 11.572h-67.293c-7.656 0-11.573-4.095-11.573-11.572v-8.9z" class=""></path></svg></span>
                      </span>
                      </a>
                    </div>
                    <div ng-if="!isChrome" class="lyric-toggle">
                    
                      <div
                        ng-click="openLyricFloatingWindow(true)"
                        class="lyric-icon"
                        ng-show="!enableLyricFloatingWindow"
                      >
                        <svg style="width: 18px;height: 18px;" t="1659234265155"  fill="currentColor" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1436" width="512" height="512"><path d="M842.32474169 18.93626311H181.79953398c-88.67069345 0-160.81272225 72.1420288-160.81272225 160.81272226v660.58734553c0 88.67069345 72.1420288 160.81272225 160.81272225 160.81272225h660.58734554c88.67069345 0 160.81272225-72.1420288 160.81272226-160.81272225V179.74898537c-0.06213783-88.67069345-72.20416663-160.81272225-160.87486009-160.81272226z m86.24731781 821.40006779c0 47.53544533-38.71187248 86.24731781-86.24731781 86.24731781H181.79953398c-47.53544533 0-86.24731781-38.71187248-86.2473178-86.24731781V179.74898537c0-47.53544533 38.71187248-86.24731781 86.2473178-86.24731782h660.58734554c47.53544533 0 86.24731781 38.71187248 86.24731781 86.24731782v660.58734553z"  fill="currentColor" p-id="1437"></path><path d="M714.25865955 193.66786085H441.5356928c-20.56762405 0-37.28270222 16.71507817-37.28270222 37.28270223s16.71507817 37.28270222 37.28270222 37.28270222h272.66082892c10.99839715 0 20.25693488 12.05474038 20.25693487 26.28430507v462.30550755c0 7.64295395-6.2137837 13.85673765-13.85673766 13.85673766h-29.14264556c-20.56762405 0-37.28270222 16.71507817-37.28270222 37.28270223s16.71507817 37.28270222 37.28270222 37.28270222h29.14264556c48.71606423 0 88.4221421-39.64394003 88.4221421-88.42214211V294.51757037c0.06213783-55.67550198-42.50228053-100.84970951-94.76020148-100.84970952z" fill="currentColor" p-id="1438"></path><path d="M465.08593303 425.75268219h170.56836267c20.56762405 0 37.28270222-16.71507817 37.28270222-37.28270222s-16.71507817-37.28270222-37.28270222-37.28270222H465.08593303c-20.56762405 0-37.28270222 16.71507817-37.28270221 37.28270222s16.71507817 37.28270222 37.28270221 37.28270222zM672.5641709 693.81531117V504.41918388c0-20.56762405-16.71507817-37.28270222-37.28270222-37.28270223H472.29392213c-20.56762405 0-37.28270222 16.71507817-37.28270222 37.28270223v189.39612729c0 20.56762405 16.71507817 37.28270222 37.28270222 37.28270222h162.98754655c20.6297619 0 37.28270222-16.71507817 37.28270222-37.28270222z m-74.56540445-37.28270222H509.57662435v-114.83072285h88.4221421v114.83072285zM243.564544 304.27321078a37.15842655 37.15842655 0 0 0 27.09209695 11.68191337c9.19639988 0 18.39279977-3.3554432 25.60078886-10.19060527a37.23920574 37.23920574 0 0 0 1.49130809-52.69288581l-38.89828599-41.19738596a37.23920574 37.23920574 0 0 0-52.69288581-1.49130809 37.23920574 37.23920574 0 0 0-1.49130808 52.69288581l38.89828598 41.19738595zM345.84342377 692.94538145l-27.09209695 14.72666738 42.31586701-311.43483923c1.42917025-10.68770797-1.80199728-21.43755378-8.88571068-29.51547259s-17.33645653-12.73825659-28.08630235-12.73825659h-101.90605275c-20.56762405 0-37.28270222 16.71507817-37.28270222 37.28270221s16.71507817 37.28270222 37.28270222 37.28270222h59.27949654l-46.4791021 341.94451723c-1.86413511 13.9188755 4.16323508 27.71347532 15.72087276 35.72925629 6.33805938 4.41178643 13.73246198 6.58661072 21.18900244 6.58661073 6.08950803 0 12.17901605-1.49130809 17.77142139-4.47392427l91.71544747-49.71026963c18.08211058-9.81777825 24.85513482-32.43595093 15.03735656-50.51806151s-32.43595093-24.91727265-50.58019934-15.16163224z"  fill="currentColor" p-id="1439"></path></svg>
                      </div>
                      <div
                        ng-click="openLyricFloatingWindow(true)"
                        class="lyric-icon"
                        ng-show="enableLyricFloatingWindow"
                      >
                      <svg style="width: 18px;height: 18px;" t="1659234282623"  fill="currentColor" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1592" width="512" height="512"><path d="M515.66613238 538.3464429h88.42214211v114.83072285h-88.42214211z" fill="currentColor" p-id="1593"></path><path d="M839.59067685 18.81198743H179.00333132c-88.67069345 0-160.81272225 72.1420288-160.81272225 160.81272226v660.58734554c0 88.67069345 72.1420288 160.81272225 160.81272225 160.81272225h660.58734553c88.67069345 0 160.81272225-72.1420288 160.81272226-160.81272225V179.56257185c-0.06213783-88.67069345-72.1420288-160.75058442-160.81272226-160.75058442zM212.24707413 207.08963365a37.23299195 37.23299195 0 0 1 52.69288582 1.4913081l38.89828597 41.19738595a37.23299195 37.23299195 0 0 1-1.49130809 52.69288581 37.03415088 37.03415088 0 0 1-25.60078885 10.19060527 37.15842655 37.15842655 0 0 1-27.09209695-11.68191336l-38.89828598-41.19738595c-14.10528901-14.97521872-13.48391063-38.5875968 1.49130808-52.69288582z m175.22870045 548.11786052l-91.71544747 49.71026962a37.42561925 37.42561925 0 0 1-38.96042382-2.11268646 37.26406087 37.26406087 0 0 1-15.72087277-35.7292563l46.4791021-341.94451721h-59.27949653c-20.56762405 0-37.28270222-16.71507817-37.28270222-37.28270222s16.71507817-37.28270222 37.28270222-37.28270222h101.96819058c10.74984581 0 21.00258892 4.66033778 28.08630234 12.73825659s10.31488095 18.88990245 8.88571069 29.5154726l-42.31586702 311.43483922 27.09209695-14.72666737c18.14424842-9.81777825 40.7624211-3.10689185 50.51806152 15.03735656 9.75564042 18.20638625 3.04475402 40.82455893-15.03735657 50.64233719z m90.90765559-27.46492398c-20.56762405 0-37.28270222-16.71507817-37.28270222-37.28270222V501.06374068c0-20.56762405 16.71507817-37.28270222 37.28270222-37.28270223h162.98754654c20.56762405 0 37.28270222 16.71507817 37.28270222 37.28270223v189.39612729c0 20.56762405-16.71507817 37.28270222-37.28270222 37.28270222H478.38343017z m-44.49069132-342.62803342c0-20.56762405 16.71507817-37.28270222 37.28270222-37.28270222h170.56836266c20.56762405 0 37.28270222 16.71507817 37.28270222 37.28270222s-16.71507817 37.28270222-37.28270222 37.28270222H471.17544107c-20.56762405 0-37.28270222-16.65294032-37.28270222-37.28270222z m381.27776805 368.35309795c0 48.71606423-39.64394003 88.4221421-88.4221421 88.42214211h-29.14264557c-20.56762405 0-37.28270222-16.71507817-37.28270222-37.28270222s16.71507817-37.28270222 37.28270222-37.28270223h29.14264557c7.64295395 0 13.85673765-6.2137837 13.85673765-13.85673766v-462.30550755c0-14.22956468-9.32067555-26.28430507-20.25693487-26.28430507H447.62520083c-20.56762405 0-37.28270222-16.71507817-37.28270222-37.28270222s16.71507817-37.28270222 37.28270222-37.28270223h272.66082892c52.32005878 0 94.82233932 45.23634537 94.82233932 100.84970952v462.30550755z"  fill="currentColor"p-id="1594"></path></svg>
                      </div>
                    </div>
                  <div
                    class="translate-switch"
                    ng-click="toggleLyricTranslation()"
                    ng-class="{selected: enableLyricTranslation,slidedown: window_type=='track'}"
                  >
                  <svg style="width:20px ;height:20px;" t="1659418908628"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1413" width="512" height="512"><path d="M512 1024C230.4 1024 0 793.6 0 512S230.4 0 512 0s512 230.4 512 512-230.4 512-512 512z m0-960C265.6 64 64 265.6 64 512s201.6 448 448 448 448-201.6 448-448S758.4 64 512 64z m268.8 422.4c-60.8-6.4-118.4-22.4-169.6-51.2-54.4 25.6-115.2 44.8-172.8 57.6l-28.8-51.2c51.2-9.6 102.4-25.6 150.4-44.8-32-28.8-57.6-64-70.4-105.6H448V240h304v51.2c-19.2 44.8-48 83.2-86.4 112 44.8 16 89.6 28.8 134.4 32l-19.2 51.2z m-92.8-192H544c16 32 38.4 57.6 67.2 76.8 28.8-19.2 57.6-44.8 76.8-76.8zM265.6 265.6L307.2 224c38.4 28.8 70.4 57.6 102.4 92.8l-41.6 41.6c-28.8-35.2-64-67.2-102.4-92.8z m99.2 396.8c16-16 32-35.2 51.2-54.4l16 64c-35.2 38.4-73.6 76.8-115.2 108.8l-22.4-54.4c9.6-9.6 16-22.4 16-35.2v-227.2H224v-57.6h140.8v256z m214.4-83.2h-112v-54.4h112v-51.2h57.6v51.2h121.6v54.4h-121.6V640h150.4v57.6h-150.4V800h-57.6v-102.4h-137.6V640h137.6v-60.8z" fill="currentColor" p-id="1414"></path></svg>
                  </div>
                    <div ng-click="toggleNowPlaying()" class="mask" ng-class="{slidedown: window_type=='track'}">
                      <?xml version="1.0" ?><!DOCTYPE svg  PUBLIC '-//W3C//DTD SVG 1.1//EN'  'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'><svg style="width: 18px;height: 18px;" fill="currentColor" enable-background="new 0 0 32 32" height="32px" id="Layer_1" version="1.1" viewBox="0 0 32 32" width="32px" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M18.221,7.206l9.585,9.585c0.879,0.879,0.879,2.317,0,3.195l-0.8,0.801c-0.877,0.878-2.316,0.878-3.194,0  l-7.315-7.315l-7.315,7.315c-0.878,0.878-2.317,0.878-3.194,0l-0.8-0.801c-0.879-0.878-0.879-2.316,0-3.195l9.587-9.585  c0.471-0.472,1.103-0.682,1.723-0.647C17.115,6.524,17.748,6.734,18.221,7.206z" fill="currentColor"/></svg>
                    </div>
                  </div>
                </div>
                </div>
                  <div
                    class="menu-modal"
                    ng-class="{slideup: !menuHidden}"
                    ng-click="togglePlaylist()"
                  ></div>
                  <div class="menu" ng-class="{slideup: !menuHidden&&playlist!=0}">
                    <div class="menu-header">
                      <span class="menu-title"
                        >{{_TOTAL_SONG_PREFIX}}{{playlist.length}}{{_TOTAL_SONG_POSTFIX}}</span
                      >
                      <a class="add-all" ng-click="showDialog(0, playlist)">
                        <svg class="icon"
                        ng-click="togglePlaylist()" t="1659349903992" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5198" width="512" height="512"><path d="M768 768v192h-128v-192h-192v-128h192v-192h128v192h192v128h-192z m64-576H192v640h192v128H64V64h896v320h-128V192z" fill="currentColor" p-id="5199"></path></svg>
                        <span>{{_ADD_TO_PLAYLIST}}</span></a
                      >
                      <a class="remove-all" ng-click="togglePlaylist()" clear-playlist=""
                        >
                        <svg clear-playlist=""  fill="currentColor"
                        class="icon" t="1659349733800" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1316" width="512" height="512"><path d="M895.464448 119.006208 677.967872 119.006208c0 0-32.8448 1.020928-58.648576-26.943488-10.395648-12.050432-27.804672-23.795712-56.4224-24.799232l-41.183232 0-6.280192 0-41.182208 0c-28.618752 1.004544-46.031872 12.749824-56.4224 24.799232-25.807872 27.964416-58.6496 26.943488-58.6496 26.943488L141.682688 119.006208c-13.99296 0-25.33888 11.34592-25.33888 25.33888l0 93.090816c-0.053248 26.927104 26.083328 26.396672 26.083328 26.396672l49.83808 0L192.265216 913.65376c0 0-3.966976 44.084224 40.121344 46.45888l269.31712 0 33.738752 0 30.808064 0.238592 38.500352 0 174.934016 0 24.297472 0 0.782336-0.238592c44.080128-2.374656 40.117248-46.45888 40.117248-46.45888L844.88192 263.832576l49.842176 0c0 0 26.133504 0.530432 26.083328-26.396672l0-93.090816C920.8064 130.353152 909.46048 119.006208 895.464448 119.006208zM430.539776 803.171328c0 17.042432-13.828096 30.865408-30.865408 30.865408-17.042432 0-30.865408-13.824-30.865408-30.865408L368.80896 320.736256c0-17.042432 13.824-30.865408 30.865408-30.865408 17.038336 0 30.865408 13.824 30.865408 30.865408L430.539776 803.171328zM663.436288 803.171328c0 17.042432-13.824 30.865408-30.865408 30.865408-17.038336 0-30.865408-13.824-30.865408-30.865408L601.705472 320.736256c0-17.042432 13.828096-30.865408 30.865408-30.865408 17.041408 0 30.865408 13.824 30.865408 30.865408L663.436288 803.171328z" fill="currentColor" p-id="1317"></path></svg>
                        <span>{{_CLEAR_ALL}}</span></a
                      >

                      <a class="close" ng-click="togglePlaylist()"
                        >
                        <svg  fill="currentColor" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="times" class="svg-inline--fa fa-times fa-w-11" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 352 512"><path fill="currentColor" d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg>
                      </a>
                    </div>
                    <ul class="menu-list">
                      <li
                        id="song{{ song.id }}"
                        ng-repeat="song in playlist track by $index"
                        ng-class="{ playing: currentPlaying.id == song.id }"
                        ng-mouseenter="playlist_highlight=true"
                        ng-mouseleave="playlist_highlight=false"
                        ng-dblclick="playById(song.id)"
                        draggable="true"
                        drag-drop-zone
                        drag-zone-object="song"
                        drag-zone-title="song.title"
                        sortable="true"
                        drag-zone-type="'application/listen1-song'"
                        drop-zone-ondrop="onCurrentPlayingSongDrop(song, arg1, arg2, arg3)"
                      >
                        <div class="song-status-icon">

                          <svg class="feather play"
                          ng-show="currentPlaying.id == song.id" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="play" class="svg-inline--fa fa-play fa-w-14" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z"></path></svg>
                        </div>
                        <div
                          class="song-title"
                          ng-class="song.disabled? 'disabled':'' "
                        >
                          <a play-from-playlist="song"
                            ><span
                              ng-if="song.source === 'xiami'"
                              style="
                                color: orange;
                                border-radius: 12px;
                                border: solid 1px;
                                padding: 0 4px;
                              "
                              >⚠️ 🦐</span
                            >{{ song.title }}</a
                          >
                        </div>
                        <div class="song-singer">
                          <a
                            ng-click="showPlaylist(song.artist_id); togglePlaylist();"
                            >{{ song.artist }}</a
                          >
                        </div>
                        <div class="tools">
                          <span
                            remove-from-playlist="song"
                            data-index="{{$index}}"
                            ng-show="playlist_highlight"
                            class="icon li-del"
                          ></span>
                          <span
                            open-url="song.source_url"
                            ng-show="playlist_highlight"
                            class="icon li-link"
                          ></span>
                        </div>
                        <!-- <div class="song-time">00:00</div> -->
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
  </div>
  </body>
  <!-- <script type="text/javascript">
    window.onload=function(){
      let li = document.querySelectorAll(".footer .cover li")
      for(let i=0;i<li.length-1;i++){
          li[i].className="hid"
        }
      li[li.length-3].className="def"
      li[li.length-2].className="a"
      li[0].className="b"
      li[1].className="c"
      li[2].className="def"
    }
  </script> -->
</html>