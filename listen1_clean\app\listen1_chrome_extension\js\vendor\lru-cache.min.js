"use strict";function Yallist(t){var e=this;if(e instanceof Yallist||(e=new Yallist),e.tail=null,e.head=null,e.length=0,t&&"function"==typeof t.forEach)t.forEach(function(t){e.push(t)});else if(arguments.length>0)for(var l=0,i=arguments.length;l<i;l++)e.push(arguments[l]);return e}function insert(t,e,l){var i=e===t.head?new Node(l,null,e,t):new Node(l,e,e.next,t);return null===i.next&&(t.tail=i),null===i.prev&&(t.head=i),t.length++,i}function push(t,e){t.tail=new Node(e,t.tail,null,t),t.head||(t.head=t.tail),t.length++}function unshift(t,e){t.head=new Node(e,null,t.head,t),t.tail||(t.tail=t.head),t.length++}function Node(t,e,l,i){if(!(this instanceof Node))return new Node(t,e,l,i);this.list=i,this.value=t,e?(e.next=this,this.prev=e):this.prev=null,l?(l.prev=this,this.next=l):this.next=null}Yallist,Yallist.Node=Node,Yallist.create=Yallist,Yallist.prototype.removeNode=function(t){if(t.list!==this)throw new Error("removing node which does not belong to this list");var e=t.next,l=t.prev;return e&&(e.prev=l),l&&(l.next=e),t===this.head&&(this.head=e),t===this.tail&&(this.tail=l),t.list.length--,t.next=null,t.prev=null,t.list=null,e},Yallist.prototype.unshiftNode=function(t){if(t!==this.head){t.list&&t.list.removeNode(t);var e=this.head;t.list=this,t.next=e,e&&(e.prev=t),this.head=t,this.tail||(this.tail=t),this.length++}},Yallist.prototype.pushNode=function(t){if(t!==this.tail){t.list&&t.list.removeNode(t);var e=this.tail;t.list=this,t.prev=e,e&&(e.next=t),this.tail=t,this.head||(this.head=t),this.length++}},Yallist.prototype.push=function(){for(var t=0,e=arguments.length;t<e;t++)push(this,arguments[t]);return this.length},Yallist.prototype.unshift=function(){for(var t=0,e=arguments.length;t<e;t++)unshift(this,arguments[t]);return this.length},Yallist.prototype.pop=function(){if(this.tail){var t=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,t}},Yallist.prototype.shift=function(){if(this.head){var t=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,t}},Yallist.prototype.forEach=function(t,e){e=e||this;for(var l=this.head,i=0;null!==l;i++)t.call(e,l.value,i,this),l=l.next},Yallist.prototype.forEachReverse=function(t,e){e=e||this;for(var l=this.tail,i=this.length-1;null!==l;i--)t.call(e,l.value,i,this),l=l.prev},Yallist.prototype.get=function(t){for(var e=0,l=this.head;null!==l&&e<t;e++)l=l.next;if(e===t&&null!==l)return l.value},Yallist.prototype.getReverse=function(t){for(var e=0,l=this.tail;null!==l&&e<t;e++)l=l.prev;if(e===t&&null!==l)return l.value},Yallist.prototype.map=function(t,e){e=e||this;for(var l=new Yallist,i=this.head;null!==i;)l.push(t.call(e,i.value,this)),i=i.next;return l},Yallist.prototype.mapReverse=function(t,e){e=e||this;for(var l=new Yallist,i=this.tail;null!==i;)l.push(t.call(e,i.value,this)),i=i.prev;return l},Yallist.prototype.reduce=function(t,e){var l,i=this.head;if(arguments.length>1)l=e;else{if(!this.head)throw new TypeError("Reduce of empty list with no initial value");i=this.head.next,l=this.head.value}for(var n=0;null!==i;n++)l=t(l,i.value,n),i=i.next;return l},Yallist.prototype.reduceReverse=function(t,e){var l,i=this.tail;if(arguments.length>1)l=e;else{if(!this.tail)throw new TypeError("Reduce of empty list with no initial value");i=this.tail.prev,l=this.tail.value}for(var n=this.length-1;null!==i;n--)l=t(l,i.value,n),i=i.prev;return l},Yallist.prototype.toArray=function(){for(var t=new Array(this.length),e=0,l=this.head;null!==l;e++)t[e]=l.value,l=l.next;return t},Yallist.prototype.toArrayReverse=function(){for(var t=new Array(this.length),e=0,l=this.tail;null!==l;e++)t[e]=l.value,l=l.prev;return t},Yallist.prototype.slice=function(t,e){(e=e||this.length)<0&&(e+=this.length),(t=t||0)<0&&(t+=this.length);var l=new Yallist;if(e<t||e<0)return l;t<0&&(t=0),e>this.length&&(e=this.length);for(var i=0,n=this.head;null!==n&&i<t;i++)n=n.next;for(;null!==n&&i<e;i++,n=n.next)l.push(n.value);return l},Yallist.prototype.sliceReverse=function(t,e){(e=e||this.length)<0&&(e+=this.length),(t=t||0)<0&&(t+=this.length);var l=new Yallist;if(e<t||e<0)return l;t<0&&(t=0),e>this.length&&(e=this.length);for(var i=this.length,n=this.tail;null!==n&&i>e;i--)n=n.prev;for(;null!==n&&i>t;i--,n=n.prev)l.push(n.value);return l},Yallist.prototype.splice=function(t,e,...l){t>this.length&&(t=this.length-1),t<0&&(t=this.length+t);for(var i=0,n=this.head;null!==n&&i<t;i++)n=n.next;var h=[];for(i=0;n&&i<e;i++)h.push(n.value),n=this.removeNode(n);null===n&&(n=this.tail),n!==this.head&&n!==this.tail&&(n=n.prev);for(i=0;i<l.length;i++)n=insert(this,n,l[i]);return h},Yallist.prototype.reverse=function(){for(var t=this.head,e=this.tail,l=t;null!==l;l=l.prev){var i=l.prev;l.prev=l.next,l.next=i}return this.head=e,this.tail=t,this};try{require("./iterator.js")(Yallist)}catch(t){};const MAX=Symbol("max"),LENGTH=Symbol("length"),LENGTH_CALCULATOR=Symbol("lengthCalculator"),ALLOW_STALE=Symbol("allowStale"),MAX_AGE=Symbol("maxAge"),DISPOSE=Symbol("dispose"),NO_DISPOSE_ON_SET=Symbol("noDisposeOnSet"),LRU_LIST=Symbol("lruList"),CACHE=Symbol("cache"),UPDATE_AGE_ON_GET=Symbol("updateAgeOnGet"),naiveLength=()=>1;class LRUCache{constructor(t){if("number"==typeof t&&(t={max:t}),t||(t={}),t.max&&("number"!=typeof t.max||t.max<0))throw new TypeError("max must be a non-negative number");this[MAX]=t.max||1/0;const e=t.length||naiveLength;if(this[LENGTH_CALCULATOR]="function"!=typeof e?naiveLength:e,this[ALLOW_STALE]=t.stale||!1,t.maxAge&&"number"!=typeof t.maxAge)throw new TypeError("maxAge must be a number");this[MAX_AGE]=t.maxAge||0,this[DISPOSE]=t.dispose,this[NO_DISPOSE_ON_SET]=t.noDisposeOnSet||!1,this[UPDATE_AGE_ON_GET]=t.updateAgeOnGet||!1,this.reset()}set max(t){if("number"!=typeof t||t<0)throw new TypeError("max must be a non-negative number");this[MAX]=t||1/0,trim(this)}get max(){return this[MAX]}set allowStale(t){this[ALLOW_STALE]=!!t}get allowStale(){return this[ALLOW_STALE]}set maxAge(t){if("number"!=typeof t)throw new TypeError("maxAge must be a non-negative number");this[MAX_AGE]=t,trim(this)}get maxAge(){return this[MAX_AGE]}set lengthCalculator(t){"function"!=typeof t&&(t=naiveLength),t!==this[LENGTH_CALCULATOR]&&(this[LENGTH_CALCULATOR]=t,this[LENGTH]=0,this[LRU_LIST].forEach(t=>{t.length=this[LENGTH_CALCULATOR](t.value,t.key),this[LENGTH]+=t.length})),trim(this)}get lengthCalculator(){return this[LENGTH_CALCULATOR]}get length(){return this[LENGTH]}get itemCount(){return this[LRU_LIST].length}rforEach(t,e){e=e||this;for(let s=this[LRU_LIST].tail;null!==s;){const i=s.prev;forEachStep(this,t,s,e),s=i}}forEach(t,e){e=e||this;for(let s=this[LRU_LIST].head;null!==s;){const i=s.next;forEachStep(this,t,s,e),s=i}}keys(){return this[LRU_LIST].toArray().map(t=>t.key)}values(){return this[LRU_LIST].toArray().map(t=>t.value)}reset(){this[DISPOSE]&&this[LRU_LIST]&&this[LRU_LIST].length&&this[LRU_LIST].forEach(t=>this[DISPOSE](t.key,t.value)),this[CACHE]=new Map,this[LRU_LIST]=new Yallist,this[LENGTH]=0}dump(){return this[LRU_LIST].map(t=>!isStale(this,t)&&{k:t.key,v:t.value,e:t.now+(t.maxAge||0)}).toArray().filter(t=>t)}dumpLru(){return this[LRU_LIST]}set(t,e,s){if((s=s||this[MAX_AGE])&&"number"!=typeof s)throw new TypeError("maxAge must be a number");const i=s?Date.now():0,h=this[LENGTH_CALCULATOR](e,t);if(this[CACHE].has(t)){if(h>this[MAX])return del(this,this[CACHE].get(t)),!1;const n=this[CACHE].get(t).value;return this[DISPOSE]&&(this[NO_DISPOSE_ON_SET]||this[DISPOSE](t,n.value)),n.now=i,n.maxAge=s,n.value=e,this[LENGTH]+=h-n.length,n.length=h,this.get(t),trim(this),!0}const n=new Entry(t,e,h,i,s);return n.length>this[MAX]?(this[DISPOSE]&&this[DISPOSE](t,e),!1):(this[LENGTH]+=n.length,this[LRU_LIST].unshift(n),this[CACHE].set(t,this[LRU_LIST].head),trim(this),!0)}has(t){if(!this[CACHE].has(t))return!1;const e=this[CACHE].get(t).value;return!isStale(this,e)}get(t){return get(this,t,!0)}peek(t){return get(this,t,!1)}pop(){const t=this[LRU_LIST].tail;return t?(del(this,t),t.value):null}del(t){del(this,this[CACHE].get(t))}load(t){this.reset();const e=Date.now();for(let s=t.length-1;s>=0;s--){const i=t[s],h=i.e||0;if(0===h)this.set(i.k,i.v);else{const t=h-e;t>0&&this.set(i.k,i.v,t)}}}prune(){this[CACHE].forEach((t,e)=>get(this,e,!1))}}const get=(t,e,s)=>{const i=t[CACHE].get(e);if(i){const e=i.value;if(isStale(t,e)){if(del(t,i),!t[ALLOW_STALE])return}else s&&(t[UPDATE_AGE_ON_GET]&&(i.value.now=Date.now()),t[LRU_LIST].unshiftNode(i));return e.value}},isStale=(t,e)=>{if(!e||!e.maxAge&&!t[MAX_AGE])return!1;const s=Date.now()-e.now;return e.maxAge?s>e.maxAge:t[MAX_AGE]&&s>t[MAX_AGE]},trim=t=>{if(t[LENGTH]>t[MAX])for(let e=t[LRU_LIST].tail;t[LENGTH]>t[MAX]&&null!==e;){const s=e.prev;del(t,e),e=s}},del=(t,e)=>{if(e){const s=e.value;t[DISPOSE]&&t[DISPOSE](s.key,s.value),t[LENGTH]-=s.length,t[CACHE].delete(s.key),t[LRU_LIST].removeNode(e)}};class Entry{constructor(t,e,s,i,h){this.key=t,this.value=e,this.length=s,this.now=i,this.maxAge=h||0}}const forEachStep=(t,e,s,i)=>{let h=s.value;isStale(t,h)&&(del(t,s),t[ALLOW_STALE]||(h=void 0)),h&&e.call(i,h.value,h.key,t)};
