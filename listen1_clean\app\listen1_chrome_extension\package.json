{"name": "listen1_chrome_extension", "version": "2.33.0", "description": "one for all free music in china", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/listen1/listen1_chrome_extension.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/listen1/listen1_chrome_extension/issues"}, "homepage": "https://github.com/listen1/listen1_chrome_extension#readme", "devDependencies": {"eslint": "^7.30.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "husky": "^4.3.8", "lint-staged": "^10.5.4", "prettier": "^2.3.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"!(**/vendor/*.js)*.js": "eslint --cache --fix", "!(**/vendor/*.js)*.{js,css,md}": "prettier --write"}}