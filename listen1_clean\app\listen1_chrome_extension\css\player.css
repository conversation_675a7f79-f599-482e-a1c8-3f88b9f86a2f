a {
  cursor: pointer;
}

.shadow {
  position: fixed;
  background: rgba(30, 30, 30, 0.5);
  _position: absolute;
  z-index: 9999;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-image: url(data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==);
}

.dialog {
  position: absolute;
  top: 120px;
  width: 480px;
  height: 420px;
  z-index: 10000;
  overflow: hidden;
  border-radius: 4px 4px 4px 4px;
  padding: 20px;
  background-color: #333;
}

.dialog-header {
  width: 100%;
  height: 30px;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 15px;
  font-weight: bold;
  text-align: left;
  border-bottom: 1px solid;
  margin-bottom: 20px;
}

.dialog-header .dialog-close {
  float: right;
  font-size: 33px;
  cursor: pointer;
  margin-top: -15px;
}

.dialog-body {
  width: 100%;
  height: 320px;
  overflow-y: auto;
  background-color: #333;
}

/*.masthead {
    z-index: 999;
}*/

.masthead,
.mastfoot {
  margin: 0 auto;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #222222;
}

.masthead {
  background-color: #333;
  height: 90px;
}

.masthead .logo {
  float: left;
  height: 50px;
  width: 50px;
  margin-right: 20px;
  cursor: pointer;
}

.masthead .masthead-brand {
  color: rgba(255, 255, 255, 1);
  cursor: pointer;
}

.cover-container {
  position: relative;
}

.container-placeholder {
  margin-top: 90px;
}

.site-wrapper {
  width: 100%;
  overflow: hidden;
  position: absolute;
  padding-left: 17px;
}

.site-wrapper-innerd {
  overflow-y: scroll;
  margin-top: 90px;
  /* uncomment the line below will hide the scroll bar */
  /*padding-right: 17px;*/
  box-sizing: content-box;
  width: 100%;
  background-color: #333;
}

.searchbox {
  /*    margin-top: 100px;*/
}

.searchbox .nav {
  margin-top: 12px;
}

.searchbox .nav .searchspinner {
  margin-top: 8px;
  height: 25px;
}

.searchitem {
  height: 92px;
}

.searchitem img {
  float: left;
  height: 90px;
  width: 90px;
}

.searchitem div {
  float: left;
  margin-left: 48px;
  margin-top: 38px;
  width: 400px;
}

.playlist-covers {
  margin-bottom: 0px;
}

.playlist-covers li {
  float: left;
  display: inline-block;
  width: 140px;
  height: 188px;
  margin-right: 22px;
}

.playlist-covers .desc {
  text-align: left;
}

.playlist-covers .u-cover {
  position: relative;
  display: block;
  width: 140px;
  height: 140px;
}

.playlist-covers .u-cover .bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 27px;
  color: #ccc;
}

.playlist-covers .loading_bottom {
  clear: both;
  text-align: center;
}

.playlist-covers .loading_bottom img {
  height: 35px;
}

.u-cover .mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.u-cover img {
  display: block;
  width: 100%;
  height: 100%;
}

.u-cover .icon-play {
  position: absolute;
  right: 10px;
  bottom: 5px;
  width: 16px;
  height: 17px;
  background: url(../images/player_directplay.png);
}

.playlist-covers .u-cover .bottom .nb {
  float: left;
  margin: 7px 0 0 0;
}

.m-playbar {
  position: absolute;
  zoom: 1;
  top: -90px;
  left: 0;
  width: 100%;
  height: 90px;
  margin: 0 auto;
  background-color: #222222;
}

.m-playbar .btns {
  width: 157px;
  padding: 27px 0 0 19px;
}

.m-playbar .btns,
.m-playbar .head,
.m-playbar .play,
.m-playbar .volum,
.m-playbar .oper {
  float: left;
}

.m-pbar .btn i {
  visibility: hidden;
  position: absolute;
  left: 5px;
  top: 5px;
  width: 12px;
  height: 12px;
  background: url(../images/loading.gif);
}

.m-pbar .barbg,
.m-pbar .cur,
.m-pbar .left {
  background: url(../images/statbar.png) no-repeat 0 9999px;
  _background-image: url(../images/statbar.png);
}

.m-playbar .btns a {
  background: url(../images/player_large.png) no-repeat 0 9999px;
  _background-image: url(../images/player_large.png);
  cursor: pointer;
}

.m-playbar .btns a {
  display: block;
  float: left;
  width: 36px;
  height: 36px;
  margin-right: 8px;
  margin-top: 0px;
  text-indent: -9999px;
}

.m-playbar .btns .previous {
  background-position: -72px 0px;
}

.m-playbar .btns .previous:hover {
  background-position: -72px -36px;
}

.m-playbar .btns .play {
  width: 36px;
  height: 36px;
  margin-top: 0;
  background-position: 0px 0px;
}

.m-playbar .btns .play:hover {
  background-position: 0px -36px;
}

.m-playbar .btns .pas {
  background-position: -108px 0px;
}

.m-playbar .btns .pas:hover {
  background-position: -108px -36px;
}

.m-playbar .btns .next {
  /* pause icon distance adjust from 36 to 38 */
  background-position: -38px 0px;
}

.m-playbar .btns .next:hover {
  background-position: -38px -36px;
}

.m-playbar .head {
  position: relative;
  margin: 10px 15px 0 0;
}

.m-playbar .head,
.m-playbar .head img {
  width: 70px;
  height: 70px;
}

.m-playbar .head .mask {
  position: absolute;
  top: 0px;
  left: 0px;
  display: block;
  width: 70px;
  height: 70px;
}

.m-playbar .maininfo {
  float: none;
  margin-left: 245px;
  margin-right: 120px;
}

.m-playbar .words .notextdeco {
  text-decoration: none;
}

.m-playbar .words {
  margin-top: 14px;
  height: 28px;
  overflow: hidden;
  color: #e8e8e8;
  text-shadow: 0 1px 0 #171717;
  line-height: 28px;
}

.m-playbar .words .name {
  max-width: 300px;
}

.m-playbar .words .by {
  max-width: 220px;
  margin-left: 15px;
  color: #9b9b9b;
}

.m-playbar .words .by a {
  color: #9b9b9b;
}

.m-playbar .words .src {
  cursor: pointer;
  float: left;
  width: 25px;
  height: 25px;
  margin: 2px 0 0 13px;
  background: url(../images/player_small.png) no-repeat 0 9999px;
  background-position: -100px 0px;
}

.m-playbar .words .src:hover {
  background-position: -100px -25px;
}

.m-playbar .words .fc1 {
  color: #e8e8e8;
  margin-left: 3px;
}

.overflowhide {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.floatleft {
  float: left;
}

.m-pbar {
  position: relative;
}

.m-pbar.play {
  width: 80%;
  margin-top: 14px;
}

.m-pbar.volume {
  position: absolute;
  right: 13px;
  bottom: 11px;
  float: right;
  width: 56%;
  margin-top: 0px;
}

.m-pbar .barbg,
.m-pbar .cur,
.m-pbar .rdy {
  height: 7px;
}

.m-pbar .barbg {
  background-position: right 0;
}

.m-pbar .cur {
  position: absolute;
  top: 0;
  left: 0;
  width: 1%;
  background-position: left -9px;
}

.m-pbar .btn {
  position: absolute;
  top: -8px;
  right: -13px;
  width: 22px;
  height: 24px;
  margin-left: -11px;
  background: url(../images/progress_indicator.png) no-repeat;
}

.m-playbar .time {
  position: absolute;
  right: -122px;
  top: -6px;
}

.m-playbar .time {
  float: right;
  margin-right: 20px;
  color: #797979;
  text-shadow: 0 1px 0 #121212;
}

.m-playbar .time em {
  color: #a1a1a1;
}

em,
i {
  font-style: normal;
  text-align: left;
  font-size: inherit;
}

.m-playbar a {
  background: url(../images/player_small.png) no-repeat 0 9999px;
}

.m-playbar .ctrl {
  position: absolute;
  right: 0px;
  bottom: 48px;
  z-index: 10;
  width: 103px;
  padding-left: 13px;
  float: none;
}

.m-playbar .icn-add {
  background-position: -25px 0px;
}

.m-playbar .icn-add:hover {
  background-position: -25px -25px;
}

.m-playbar .icn-list {
  background-position: -125px 0px;
}

.m-playbar .icn-vol {
  background-position: -175px 0px;
}

.m-playbar .icn-vol-mute {
  background-position: -200px 0px;
}

.m-playbar .icn-list:hover {
  background-position: -125px -25px;
}

.m-playbar .icn-loop {
  background-position: -50px 0px;
}

.m-playbar .icn-loop:hover {
  background-position: -50px -25px;
}

.m-playbar .icn-vol:hover {
  background-position: -175px -25px;
}

.m-playbar .icn-vol-mute:hover {
  background-position: -200px -25px;
}

.m-playbar .icn-shuffle {
  background-position: -150px 0px;
}

.m-playbar .icn-shuffle:hover {
  background-position: -150px -25px;
}

.m-playbar .icn-repeatone {
  background-position: -225px 0px;
}

.m-playbar .icn-repeatone:hover {
  background-position: -225px -25px;
}

.m-playbar .icn {
  float: left;
  width: 25px;
  height: 25px;
  margin: 11px 2px 0 0;
  text-indent: -9999px;
}

.m-playbar .icn-add {
  margin-right: 5px;
}

.m-playbar .menu {
  position: absolute;
  bottom: 90px;
  _bottom: 90px;
  right: 0px;
  _right: 0px;
  height: 349px;
  width: 60%;
  background-color: #121212;
}

.m-playbar .menu ul {
  display: inline-block;
  padding-left: 0px;
  height: 308px;
  overflow-y: scroll;
  margin-bottom: 0px;
}

.m-playbar .menu li {
  float: left;
  width: 100%;
  display: block;
}

.m-playbar .menu .lyric {
  text-align: center;
  width: 39%;
  display: inline-block;
  height: 308px;
  overflow-y: scroll;
  position: relative;
}

.m-playbar .menu .lyric p {
  min-height: 20px;
}

.m-playbar .menu .lyric .placeholder {
  height: 50px;
}

.m-playbar .menu .lyric .highlight {
  font-size: 15px;
  color: #ffffff;
}

.m-playbar .menu .playing {
  background-color: #555555;
}

.m-playbar .menu li:hover,
.m-playbar .menu li:focus {
  background-color: #999999;
}

.m-playbar .menu .icn-remove {
  height: 20px;
  width: 20px;
  background-position: -75px -25px;
  display: inline-block;
}

.m-playbar .menu .icn-remove:hover {
  background-position: -75px -25px;
}

.volume-ctrl {
  position: absolute;
  right: 0px;
  bottom: 16px;
  width: 110px;
}

li {
  list-style: none;
}

.menu-header {
  height: 40px;
  background-color: #222222;
  padding-top: 4px;
  text-align: center;
}

.menu-header span {
  position: absolute;
  left: 19px;
  top: 7px;
  font-size: 18px;
  color: #ffffff;
}

.menu-header small {
  background-color: #333333;
  color: #ffffff;
  cursor: pointer;
  vertical-align: middle;
  display: inline-block;
  width: 60px;
  line-height: 20px;
}

.menu-header a:hover small {
  background-color: #ffffff;
  color: #333333;
}
.menu .add-all {
  display: inline-block;
  position: absolute;
  left: 335px;
  top: 9px;
}

.menu .remove-all {
  display: inline-block;
  position: absolute;
  left: 410px;
  top: 9px;
}

.menu .close-popup {
  float: right;
  margin-right: 14px;
  font-size: 20px;
  text-decoration: none;
  color: #aaaaaa;
}

.menu .close-popup:hover {
  color: #ffffff;
}

.menu .title {
  width: 300px;
  float: left;
  height: 28px;
  padding-top: 3px;
  text-align: left;
  padding-left: 20px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}

.menu .singer {
  width: 180px;
  float: right;
  height: 28px;
  padding-top: 3px;
  text-align: left;
  padding-left: 20px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}

.dbimport {
  /*margin-top: 100px;*/
}

.form-signin {
  width: 300px;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.form-signin .form-control,
.form-signin .valid-img,
.form-signin .btn {
  margin-top: 10px;
}

.form-signin .valid-img {
  height: 40px;
  width: 220px;
}

.form-signin .security-notice {
  margin-top: 10px;
}

.playlist-detail {
  position: absolute;
  text-align: left;
  background-color: #333;
  width: 100%;
}

.playlist-detail .detail-head {
  width: 200px;
  position: fixed;
  margin-bottom: 20px;
}

.playlist-detail .detail-head-cover {
  height: 180px;
  /*    width: 225px;*/
  float: left;
  margin: 10px;
}

.playlist-detail .detail-head-cover img {
  max-width: 100%;
  max-height: 100%;
}

.detail-head-title {
  float: left;
  width: 100%;
  text-align: center;
}

.detail-head-title a {
  display: inline-block;
  text-indent: -9999px;
  width: 36px;
  height: 36px;
  margin-top: 0;
  background: url(../images/player_large.png) no-repeat 0 9999px;
}

.detail-head-title .play {
  background-position: 0px 0px;
}

.detail-head-title .play:hover {
  background-position: 0px -36px;
}

.detail-head-title .add {
  background-position: -216px 0px;
}

.detail-head-title .add:hover {
  background-position: -216px -36px;
}

.detail-head-title .link {
  background-position: -250px 0px;
}

.detail-head-title .link:hover {
  background-position: -250px -36px;
}

.detail-head-title .edit {
  background-position: -288px 0px;
}

.detail-head-title .edit:hover {
  background-position: -288px -36px;
}

.detail-head-title .clone {
  background-position: -144px 0px;
}

.detail-head-title .clone:hover {
  background-position: -144px -36px;
}

.detail-head-title .merge {
  background-position: -324px 0px;
}

.detail-head-title .merge:hover {
  background-position: -324px -36px;
}

.detail-head-title .ply:hover {
  background-position: -40px -204px;
}

.playlist-detail .detail-head-title h2 {
  font-size: 17px;
  margin-bottom: 35px;
}

.playlist-detail .detail-songlist {
  margin-left: 220px;
  margin-top: 6px;
  margin-right: 14px;
}

.playsong-detail .detail-head {
  width: 390px;
  position: fixed;
  margin-bottom: 20px;
}

.playsong-detail .detail-songinfo {
  padding-left: 440px;
  padding-right: 55px;
}

.playsong-detail .detail-head .detail-head-cover {
  margin: 0 auto;
  width: 200px;
}

.playsong-detail .detail-head .detail-head-cover img {
  width: 240px;
  height: 240px;
}

.playsong-detail .detail-songinfo h2 {
  font-size: 22px;
}

.playsong-detail .detail-songinfo .info {
  border-bottom: solid #444 1px;
  margin-bottom: 5px;
  padding-bottom: 10px;
}
.playsong-detail .detail-songinfo .info span {
  color: #9b9b9b;
  margin-right: 10px;
}
.playsong-detail .detail-songinfo .info span.album {
  margin-left: 30px;
}
.playsong-detail .lyric {
  color: #999; /* IE8 proofing */
  color: rgba(255, 255, 255, 0.5);
  text-align: left;
  width: 100%;
  display: inline-block;
  height: 410px;
  overflow-y: scroll;
  position: relative;
  font-size: 15px;
}

.playsong-detail .lyric p {
  min-height: 20px;
}

.playsong-detail .lyric .placeholder {
  height: 50px;
}

.playsong-detail .lyric .highlight {
  color: #ffffff;
}

.detail-songlist {
  padding-left: 0px;
  text-align: left;
}

.detail-songlist li {
  float: left;
  width: 100%;
  display: block;
  padding: 10px;
}

.detail-songlist .col2 {
  float: left;
  width: 28%;
  margin-left: 2%;
  font-size: 15px;
}

.detail-songlist .col1 {
  float: left;
  width: 19%;
  margin-left: 2%;
}

.detail-songlist .disabled {
  color: #777777;
}

.detail-songlist .col-add {
  float: left;
  width: 75px;
  margin-left: 2%;
}

.detail-songlist .detail-tools {
  float: right;
  height: 21px;
  position: relative;
  width: 118px;
}

.detail-songlist .detail-tools a {
  background: url(../images/player_small.png) no-repeat 0 9999px;
  height: 25px;
  width: 25px;
  cursor: pointer;
}

.detail-songlist .detail-tools .detail-add-button {
  background-position: 0px 0px;
}

.detail-songlist .detail-tools .detail-fav-button {
  background-position: -25px 0px;
}

.detail-songlist .detail-tools .detail-delete-button {
  background-position: -75px 0px;
}

.detail-songlist .detail-tools .source-button {
  background-position: -100px 0px;
}

.detail-songlist .detail-tools .detail-add-button:hover {
  background-position: 0px -25px;
}

.detail-songlist .detail-tools .detail-fav-button:hover {
  background-position: -25px -25px;
}

.detail-songlist .detail-tools .detail-delete-button:hover {
  background-position: -75px -25px;
}

.detail-songlist .detail-tools .source-button:hover {
  background-position: -100px -25px;
}

.detail-songlist .detail-tools a {
  text-decoration: none;
  display: inline-block;
}

.detail-songlist .detail-artist a {
  color: #777777;
}

.detail-songlist .odd {
  background-color: #333;
}

.detail-songlist .even,
.detail-songlist .detail-add {
  background-color: #2d2d2d;
}

.dialog .detail-songlist li:hover {
  background-color: #999999;
  cursor: pointer;
}

/*.playlist-detail .detail-songlist li:hover {
    background-color: #999999;
}*/

.playlist-detail .btn {
  width: 88px;
  margin-top: 0;
  float: left;
}

.cover-container .detail-close {
  position: absolute;
  right: -32px;
  top: 0px;
}

.cover-container .detail-close span {
  font-size: 34px;
  cursor: pointer;
  color: #aaaaaa;
}

.cover-container .detail-close span:hover {
  color: #ffffff;
}

.dialog-playlist {
  padding-left: 0px;
  text-align: left;
}

.dialog-playlist li {
  cursor: pointer;
  height: 112px;
  padding: 6px;
}

.dialog-playlist li:hover {
  background-color: #555555;
}

.dialog-playlist li img {
  float: left;
  height: 100px;
  width: 100px;
}

.dialog-playlist li h2 {
  margin-left: 125px;
  font-size: 17px;
}

.dialog-backuplist {
  padding-left: 0px;
  text-align: left;
}

.dialog-backuplist li {
  cursor: pointer;
  height: 112px;
  padding: 6px;
}

.dialog-backuplist li:hover {
  background-color: #555555;
}

.dialog-backuplist li img {
  float: left;
  height: 100px;
  width: 100px;
}

.dialog-backuplist li h2 {
  margin-top: 10px;
  margin-left: 125px;
  font-size: 15px;
}

.dialog-merge-playlist {
  padding-left: 0px;
  text-align: left;
}

.dialog-merge-playlist li {
  cursor: pointer;
  height: 112px;
  padding: 6px;
}

.dialog-merge-playlist li:hover {
  background-color: #555555;
}

.dialog-merge-playlist li img {
  float: left;
  height: 100px;
  width: 100px;
}

.dialog-merge-playlist li h2 {
  margin-left: 125px;
  font-size: 17px;
}

.dialog-newplaylist input {
  margin-bottom: 22px;
}

.dialog-newplaylist .confirm-button {
  margin-left: 76px;
  margin-right: 96px;
}

.dialog-newbackup {
  text-align: center;
}

.dialog-newbackup .confirm-button {
  margin-right: 12px;
}

.dialog-editplaylist .dialog-footer {
  position: absolute;
  bottom: 20px;
}

.dialog-editplaylist .confirm-button,
.dialog-open-url .confirm-button {
  margin-right: 82px;
  margin-left: 93px;
}

.dialog-connect-lastfm .buttons {
  margin-top: 30px;
}

.dialog-connect-lastfm .confirm-button {
  margin-left: 40px;
  margin-right: 48px;
}

.source-list {
  position: absolute;
  right: -32px;
  top: 0px;
  z-index: 10;
  text-align: center;
}

.source-list div {
  background-color: #333333;
  color: #ffffff;
  height: 35px;
  border: 1px solid #ffffff;
  width: 75px;
  cursor: pointer;
  vertical-align: middle;
  padding-top: 6px;
}

.source-list div:first-child:not(:last-child) {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.source-list div:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}

.source-list .active {
  background-color: #e6e6e6;
  color: #333333;
}

.source-list div:hover {
  background-color: #ffffff;
  color: #333333;
}

.source-list .open-url-button {
  border-radius: 4px;
}

.settings-title {
  font-size: 20px;
  padding: 20px;
  border-bottom: 2px solid #aaaaaa;
}

.settings-content {
  padding: 20px;
}

.settings-content .btn {
  margin-right: 10px;
}

.btn-group button,
.btn-pagination,
.btn-pagination:focus {
  background-color: #333333;
  color: #ffffff;
  border-color: #333333;
}

.btn-group button:hover,
.btn-pagination:hover {
  background-color: #ffffff;
  color: #333333;
}

.searchbox li > a:hover {
  color: #333333;
}

.search-pagination {
  text-align: center;
  display: block;
  vertical-align: middle;
  line-height: 45px;
}

.search-pagination button:focus {
  outline: 0;
}

.search-pagination label {
  margin: 0 15px;
}
